{# 這個檔案已經不再作為完整頁面使用，只保留表單內容，方便 index.html include #}
<form action="/transfer" method="post" enctype="multipart/form-data">
  <div class="box" style="max-width: 600px; margin: auto;">
    <h2 class="title is-4 has-text-link">資金移轉紀錄新增</h2>
    <div class="columns">
      <div class="column">
        <div class="field">
          <label class="label">轉出帳號</label>
          <div class="control">
            <div class="select is-fullwidth">
              <select name="out_account" id="out_account_select">
                <option value="">選擇帳號</option>
                {% for acc in accounts %}
                  <option value="{{ acc.id }}" data-subject-code="{{ acc.subject_code }}">{{ acc.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
      </div>
      <div class="column">
        <div class="field">
          <label class="label">轉入帳號</label>
          <div class="control">
            <div class="select is-fullwidth">
              <select name="in_account" id="in_account_select">
                <option value="">選擇帳號</option>
                {% for acc in accounts %}
                  <option value="{{ acc.id }}" data-subject-code="{{ acc.subject_code }}">{{ acc.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="columns">
      <div class="column">
        <div class="field">
          <label class="label">會計科目</label>
          <div class="control">
            <input class="input" type="text" id="subject_out" name="subject_out" placeholder="未產生會計科目" readonly>
          </div>
        </div>
      </div>
      <div class="column">
        <div class="field">
          <label class="label">會計科目</label>
          <div class="control">
            <input class="input" type="text" id="subject_in" name="subject_in" placeholder="未產生會計科目" readonly>
          </div>
        </div>
      </div>
    </div>
    <div class="columns">
      <div class="column">
        <div class="field">
          <label class="label">總金額</label>
          <div class="control">
            <input class="input" type="number" name="amount" value="0">
          </div>
        </div>
      </div>
      <div class="column">
        <div class="field">
          <label class="label">手續費</label>
          <div class="control">
            <input class="input" type="number" name="fee" value="0">
          </div>
        </div>
      </div>
    </div>
    <div class="field">
      <label class="label">備註</label>
      <div class="control">
        <textarea class="textarea" name="note" placeholder="備註"></textarea>
      </div>
    </div>
    <div class="columns">
      <div class="column">
        <div class="field">
          <label class="label">移轉日期</label>
          <div class="control">
            <input class="input" type="date" name="transfer_date" value="2025-07-10">
          </div>
        </div>
      </div>
      <div class="column">
        <div class="field">
          <label class="label">憑證圖檔</label>
          <div class="control">
            <div class="upload-box">
              <span class="icon"><i class="fas fa-upload"></i></span>
              <span>上傳憑證圖</span>
              <input type="file" name="voucher" style="display:none;">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="field is-grouped is-grouped-right">
      <p class="control">
        <button class="button is-light" type="reset">取消</button>
      </p>
      <p class="control">
        <button class="button is-link" type="submit">儲存</button>
      </p>
    </div>
  </div>
</form>
<script>
document.addEventListener('DOMContentLoaded', function() {
  // 轉出帳號
  var select = document.getElementById('out_account_select');
  var subjectInput = document.getElementById('subject_out');
  select.addEventListener('change', function() {
    var selected = select.options[select.selectedIndex];
    var subjectCode = selected.getAttribute('data-subject-code') || '';
    subjectInput.value = subjectCode;
  });

  // 轉入帳號
  var inSelect = document.getElementById('in_account_select');
  var inSubjectInput = document.getElementById('subject_in');
  inSelect.addEventListener('change', function() {
    var selected = inSelect.options[inSelect.selectedIndex];
    var subjectCode = selected.getAttribute('data-subject-code') || '';
    inSubjectInput.value = subjectCode;
  });
});
</script> 