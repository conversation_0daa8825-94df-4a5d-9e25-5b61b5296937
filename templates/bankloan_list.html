<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>銀行借款列表</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
    }

    .column.is-narrow {
      flex: none;
      width: 200px !important;
      max-width: 200px !important;
      min-width: 200px !important;
    }

    .sidebar {
      max-width: 200px;
      min-width: 180px;
      width: 200px;
    }

    .sidebar .menu-label {
      font-size: 14px;
      font-weight: bold;
    }

    .sidebar .menu-list a {
      font-size: 20px;
      padding: 0.5em 0.75em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .main-content {
      padding: 2rem 1rem;
    }

    .header-section {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;
    }

    .back-link {
      font-size: 1.2rem;
      color: #363636;
      text-decoration: none;
      margin-right: 1rem;
    }

    .back-link:hover {
      color: #3273dc;
    }

    .header-buttons {
      margin-left: auto;
      display: flex;
      gap: 0.5rem;
    }

    .table-container {
      background: white;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
    }

    .table {
      margin-bottom: 0;
    }

    .table th {
      background-color: #3273dc;
      color: white;
      font-weight: 600;
      border: none;
      padding: 1rem 0.75rem;
      text-align: center;
      vertical-align: middle;
    }

    .table td {
      padding: 0.75rem;
      text-align: center;
      vertical-align: middle;
      border-bottom: 1px solid #dbdbdb;
    }

    .table tbody tr:hover {
      background-color: #fafafa;
    }

    .action-buttons {
      display: flex;
      gap: 0.25rem;
      justify-content: center;
    }

    .action-buttons .button {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }
  </style>
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <div class="main-content">
          <div class="header-section">
            <a href="/?main=資金管理" class="back-link">← 銀行借款列表</a>
            <div class="header-buttons">
              <button class="button is-info is-light">頁面指南</button>
              <a href="/bankloan/create" class="button is-link">新增銀行借款</a>
            </div>
          </div>

          <div class="table-container">
            <table class="table is-fullwidth">
              <thead>
                <tr>
                  <th>銀行</th>
                  <th>借款會計項目</th>
                  <th>借款帳戶</th>
                  <th>本金</th>
                  <th>手續費</th>
                  <th>借款日期</th>
                  <th>備註</th>
                  <th>憑證圖檔</th>
                  <th>編輯</th>
                </tr>
              </thead>
              <tbody>
                {% if bankloan_records %}
                  {% for record in bankloan_records %}
                  <tr>
                    <td>{{ record.bank_name or '-' }}</td>
                    <td>{{ record.subject_code or '-' }}</td>
                    <td>{{ record.account_name or '-' }}</td>
                    <td>${{ '{:,.0f}'.format(record.principal or 0) }}</td>
                    <td>${{ '{:,.0f}'.format(record.fee or 0) }}</td>
                    <td>{{ record.loan_date or '-' }}</td>
                    <td>{{ record.note or '-' }}</td>
                    <td>
                      {% if record.voucher_image %}
                        <a href="{{ record.voucher_image }}" target="_blank">查看</a>
                      {% else %}-{% endif %}
                    </td>
                    <td>
                      <a href="/bankloan/edit/{{ record.id }}" class="button is-small is-info">編輯</a>
                    </td>
                  </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td colspan="9" class="has-text-grey">目前沒有銀行借款記錄</td>
                  </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
  <script>
    function deleteRecord(id) {
      if (confirm('確定要刪除這筆銀行借款記錄嗎？')) {
        // 這裡可以發送 AJAX 請求到後端刪除記錄
        fetch(`/bankloan/delete/${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          }
        })
        .then(response => {
          if (response.ok) {
            location.reload();
          } else {
            alert('刪除失敗，請稍後再試');
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('刪除失敗，請稍後再試');
        });
      }
    }
  </script>
</body>

</html>
