<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帳戶設定</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
        }

        .column.is-narrow {
            flex: none;
            width: 200px !important;
            max-width: 200px !important;
            min-width: 200px !important;
        }

        .sidebar {
            max-width: 200px;
            min-width: 180px;
            width: 200px;
        }

        .sidebar .menu-label {
            font-size: 14px;
            font-weight: bold;
        }

        .sidebar .menu-list a {
            font-size: 20px;
            padding: 0.5em 0.75em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .container-box {
            max-width: 1400px;
            margin: 40px auto;
        }

        .main-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 2.5rem 2rem;
            max-width: 900px;
            margin: 2rem auto;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 1.2rem;
        }

        .account-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #e9ecef;
            text-decoration: none;
            display: block;
        }

        .account-card:hover {
            background: #e9ecef;
            transition: background 0.3s;
            text-decoration: none;
        }

        .account-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .account-desc {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .add-btn {
            float: right;
            margin-top: -0.5em;
        }

        .account-card .icon {
            margin-right: 0.5rem;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=設定"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        帳戶設定
                    </h1>
                </div>
                <div class="main-card">
                    <div class="section-title mb-5">
                        帳戶管理
                        <div class="add-btn">
                            <div class="dropdown is-hoverable">
                                <div class="dropdown-trigger">
                                    <button class="button is-link is-small">
                                        <span>＋ 新增帳戶</span>
                                        <span class="icon is-small">
                                            <i class="fas fa-angle-down"></i>
                                        </span>
                                    </button>
                                </div>
                                <div class="dropdown-menu" id="dropdown-menu" role="menu">
                                    <div class="dropdown-content">
                                        <a href="/account/add/cash" class="dropdown-item">
                                            <span class="icon"><i class="fas fa-money-bill"></i></span>
                                            <span>新增現金帳戶</span>
                                        </a>
                                        <a href="/account/add/bank" class="dropdown-item">
                                            <span class="icon"><i class="fas fa-university"></i></span>
                                            <span>新增銀行帳戶</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="columns">
                        <div class="column is-6">
                            <a href="/account/cash" class="account-card">
                                <div class="account-title">
                                    <span class="icon"><i class="fas fa-money-bill"></i></span>
                                    現金帳戶
                                </div>
                                <div class="account-desc">
                                    管理現金帳戶，包含現金收付記錄
                                </div>
                            </a>
                        </div>
                        <div class="column is-6">
                            <a href="/account/bank" class="account-card">
                                <div class="account-title">
                                    <span class="icon"><i class="fas fa-university"></i></span>
                                    銀行帳戶
                                </div>
                                <div class="account-desc">
                                    管理銀行帳戶，包含存款、提款記錄
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>