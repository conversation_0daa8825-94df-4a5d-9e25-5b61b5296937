<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>編輯子科目</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        .main-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 2.5rem 2rem;
            max-width: 600px;
            margin: 2rem auto;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 1.2rem;
        }

        .is-readonly {
            background: #f5f6fa;
            color: #888;
        }
    </style>
</head>

<body style="background:#f5f6fa;">
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="main-card">
                    <div class="section-title mb-5">
                        <a href="javascript:history.back()"
                            style="color:#2563eb;text-decoration:none;font-size:1.1rem;">←</a>
                        編輯子科目
                    </div>
                    <form method="post">
                        <input type="hidden" name="code" value="{{ subject.code }}">
                        <div class="field mb-3">
                            <label class="label">子科目代碼</label>
                            <div class="control">
                                <input class="input is-readonly" type="text" value="{{ subject.code }}" readonly>
                            </div>
                        </div>
                        <div class="field mb-3">
                            <label class="label">子科目名稱</label>
                            <div class="control">
                                <input class="input" name="sub_name" type="text" value="{{ subject.name }}" required>
                            </div>
                        </div>
                        <div class="field mb-3">
                            <label class="label">備註</label>
                            <div class="control">
                                <textarea class="textarea" name="sub_note"
                                    placeholder="備註">{{ subject.note }}</textarea>
                            </div>
                        </div>
                        <div class="field is-grouped is-grouped-right mt-4">
                            <div class="control">
                                <a class="button is-light" href="javascript:history.back()">取消</a>
                            </div>
                            <div class="control">
                                <button class="button is-link" type="submit">儲存</button>
                            </div>
                        </div>
                    </form>
                    <!-- 刪除按鈕獨立一個 form -->
                    <form method="post" action="/accounting/delete_subject" style="display:inline;">
                        <input type="hidden" name="code" value="{{ subject.code }}">
                        <button class="button is-danger" type="submit"
                            onclick="return confirm('確定要刪除這個子科目嗎？')">刪除</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>

</html>