<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>新增投資記錄</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
    }

    .column.is-narrow {
      flex: none;
      width: 200px !important;
      max-width: 200px !important;
      min-width: 200px !important;
    }

    .sidebar {
      max-width: 200px;
      min-width: 180px;
      width: 200px;
    }

    .sidebar .menu-label {
      font-size: 14px;
      font-weight: bold;
    }

    .sidebar .menu-list a {
      font-size: 20px;
      padding: 0.5em 0.75em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .main-content {
      padding: 2rem 1rem;
    }

    .form-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .form-header {
      background: #3273dc;
      color: white;
      padding: 1rem 1.5rem;
      font-size: 1.2rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .back-link {
      color: white;
      text-decoration: none;
      font-size: 1.1rem;
    }

    .back-link:hover {
      color: #f0f0f0;
    }

    .form-content {
      padding: 2rem;
    }

    .field-group {
      margin-bottom: 1.5rem;
    }

    .field-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .field-row .field {
      flex: 1;
    }

    .submit-section {
      text-align: center;
      padding-top: 1.5rem;
      border-top: 1px solid #dbdbdb;
      margin-top: 2rem;
    }

    .select-full {
      width: 100%;
    }

    .calculation-section {
      background: #f8f9fa;
      padding: 1rem;
      border-radius: 6px;
      margin: 1rem 0;
    }

    .calculation-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .calculation-row:last-child {
      margin-bottom: 0;
      font-weight: 600;
      font-size: 1.1rem;
      border-top: 1px solid #dee2e6;
      padding-top: 0.5rem;
    }
  </style>
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <!-- 頁面標題 -->
        <div class="mb-4">
          <h1 class="title is-4">
            <a href="/investment_manage"
              style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
            新增投資記錄
          </h1>
        </div>
        <div class="main-content">
          <div class="form-container">
            <div class="form-header">
              <button class="button is-info is-light">頁面指南</button>
            </div>
            
            <div class="form-content">
              <form method="POST" enctype="multipart/form-data">
                <!-- 基本資訊 -->
                <div class="field-group">
                  <h4 class="subtitle is-5">基本資訊</h4>
                  <div class="field-row">
                    <div class="field">
                      <label class="label">投資代碼 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <input class="input" type="text" name="code" placeholder="例：2330" required>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">投資名稱 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <input class="input" type="text" name="name" placeholder="例：台積電" required>
                      </div>
                    </div>
                  </div>
                  
                  <div class="field-row">
                    <div class="field">
                      <label class="label">投資類型 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="type" required>
                            <option value="">請選擇投資類型</option>
                            <option value="股票">股票</option>
                            <option value="基金">基金</option>
                            <option value="債券">債券</option>
                            <option value="ETF">ETF</option>
                            <option value="其他">其他</option>
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">投資帳戶</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="account_id">
                            <option value="">請選擇投資帳戶</option>
                            <!-- 這裡可以從後端傳入帳戶資料 -->
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 購買資訊 -->
                <div class="field-group">
                  <h4 class="subtitle is-5">購買資訊</h4>
                  <div class="field-row">
                    <div class="field">
                      <label class="label">購買日期 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <input class="input" type="date" name="purchase_date" required>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">購買價格 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <input class="input" type="number" name="purchase_price" step="0.01" placeholder="0.00" required onchange="calculateTotal()">
                      </div>
                    </div>
                  </div>
                  
                  <div class="field-row">
                    <div class="field">
                      <label class="label">購買數量 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <input class="input" type="number" name="quantity" placeholder="0" required onchange="calculateTotal()">
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">手續費</label>
                      <div class="control">
                        <input class="input" type="number" name="commission" step="0.01" placeholder="0.00" onchange="calculateTotal()">
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 計算區域 -->
                <div class="calculation-section">
                  <div class="calculation-row">
                    <span>購買金額：</span>
                    <span id="purchase-amount">0</span>
                  </div>
                  <div class="calculation-row">
                    <span>手續費：</span>
                    <span id="commission-amount">0</span>
                  </div>
                  <div class="calculation-row">
                    <span>總投資成本：</span>
                    <span id="total-cost">0</span>
                  </div>
                </div>

                <!-- 目前狀況 -->
                <div class="field-group">
                  <h4 class="subtitle is-5">目前狀況</h4>
                  <div class="field-row">
                    <div class="field">
                      <label class="label">目前價格</label>
                      <div class="control">
                        <input class="input" type="number" name="current_price" step="0.01" placeholder="0.00" onchange="calculateProfit()">
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">持有狀態</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="status">
                            <option value="持有中">持有中</option>
                            <option value="已賣出">已賣出</option>
                            <option value="部分賣出">部分賣出</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 其他資訊 -->
                <div class="field-group">
                  <h4 class="subtitle is-5">其他資訊</h4>
                  <div class="field-row">
                    <div class="field">
                      <label class="label">部門</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="department_id">
                            <option value="">請選擇部門</option>
                            <!-- 這裡可以從後端傳入部門資料 -->
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">專案</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="project_id">
                            <option value="">請選擇專案</option>
                            <!-- 這裡可以從後端傳入專案資料 -->
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="field">
                    <label class="label">備註</label>
                    <div class="control">
                      <textarea class="textarea" name="note" rows="3" placeholder="請輸入備註"></textarea>
                    </div>
                  </div>
                </div>

                <!-- 提交按鈕 -->
                <div class="submit-section">
                  <button type="submit" class="button is-primary is-large">儲存</button>
                  <a href="/investment_manage" class="button is-light is-large ml-3">取消</a>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
  <script>
    // 設定今天的日期為預設值
    document.addEventListener('DOMContentLoaded', function() {
      const today = new Date().toISOString().split('T')[0];
      document.querySelector('input[name="purchase_date"]').value = today;
    });

    // 計算總投資成本
    function calculateTotal() {
      const price = parseFloat(document.querySelector('input[name="purchase_price"]').value) || 0;
      const quantity = parseFloat(document.querySelector('input[name="quantity"]').value) || 0;
      const commission = parseFloat(document.querySelector('input[name="commission"]').value) || 0;
      
      const purchaseAmount = price * quantity;
      const totalCost = purchaseAmount + commission;
      
      document.getElementById('purchase-amount').textContent = purchaseAmount.toLocaleString();
      document.getElementById('commission-amount').textContent = commission.toLocaleString();
      document.getElementById('total-cost').textContent = totalCost.toLocaleString();
    }

    // 計算損益
    function calculateProfit() {
      const purchasePrice = parseFloat(document.querySelector('input[name="purchase_price"]').value) || 0;
      const currentPrice = parseFloat(document.querySelector('input[name="current_price"]').value) || 0;
      const quantity = parseFloat(document.querySelector('input[name="quantity"]').value) || 0;
      const commission = parseFloat(document.querySelector('input[name="commission"]').value) || 0;
      
      if (purchasePrice > 0 && currentPrice > 0 && quantity > 0) {
        const purchaseCost = (purchasePrice * quantity) + commission;
        const currentValue = currentPrice * quantity;
        const profitLoss = currentValue - purchaseCost;
        const profitLossRate = (profitLoss / purchaseCost) * 100;
        
        console.log('損益計算:', {
          purchaseCost,
          currentValue,
          profitLoss,
          profitLossRate: profitLossRate.toFixed(2) + '%'
        });
      }
    }
  </script>
</body>

</html>
