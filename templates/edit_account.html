<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>編輯帳戶</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .container-box {
            max-width: 1400px;
            margin: 40px auto;
        }

        .main-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 2.5rem 2rem;
            max-width: 600px;
            margin: 2rem auto;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 1.2rem;
        }

        .back-btn {
            margin-bottom: 1rem;
        }
    </style>
</head>

<body>
    <div class="container is-fluid container-box">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        {% if account.category == '現金' %}
                        <a href="/account/cash"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        {% else %}
                        <a href="/account/bank"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        {% endif %}
                        編輯{{ account.category }}帳戶
                    </h1>
                </div>
                <div class="main-card">
                    <div class="section-title mb-5">
                        帳戶資訊
                    </div>

                    <form method="POST" enctype="multipart/form-data">
                        {% if account.category == '銀行帳戶' %}
                        <div class="field">
                            <label class="label">帳戶名稱 *</label>
                            <div class="control">
                                <input class="input" type="text" name="name" value="{{ account.name }}" required>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">銀行名稱 *</label>
                            <div class="control">
                                <div class="select is-fullwidth">
                                    <select id="bank-head-select" name="bank_name" required>
                                        <option value="">請選擇銀行</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control mt-2" id="otherBankField" style="display: none;">
                                <input class="input" type="text" name="other_bank_name" placeholder="請輸入銀行名稱"
                                    value="{{ account.other_bank_name or '' }}">
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">分行</label>
                            <div class="control">
                                <div class="select is-fullwidth">
                                    <select id="bank-branch-select" name="branch">
                                        <option value="">請先選擇銀行</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">帳號 *</label>
                            <div class="control">
                                <input class="input" type="text" name="account_number"
                                    value="{{ account.account_number or '' }}" required>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">戶名 *</label>
                            <div class="control">
                                <input class="input" type="text" name="account_holder"
                                    value="{{ account.account_holder or '' }}" required>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">科目代碼</label>
                            <div class="control">
                                <input class="input" type="text" name="subject_code"
                                    value="{{ account.subject_code or '' }}">
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">期初金額</label>
                            <div class="control">
                                <input class="input" type="text" name="init_amount"
                                    value="{{ account.init_amount or '' }}">
                            </div>
                        </div>
                        <div class="field">
                            <div class="control">
                                <label class="checkbox">
                                    <input type="checkbox" name="is_default" {% if account.is_default %}checked{% endif
                                        %}> 預設帳戶
                                </label>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">備註</label>
                            <div class="control">
                                <textarea class="textarea" name="note"
                                    placeholder="請輸入備註說明">{{ account.note or '' }}</textarea>
                            </div>
                        </div>
                        {% endif %}
                        <div class="field is-grouped is-grouped-centered mt-5">
                            <div class="control">
                                <button type="submit" class="button is-link is-medium">儲存修改</button>
                            </div>
                            <div class="control">
                                <button type="button" class="button is-danger is-medium">刪除</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 銀行總行下拉選單
            const bankSelect = document.getElementById('bank-head-select');
            const branchSelect = document.getElementById('bank-branch-select');
            const otherBankField = document.getElementById('otherBankField');
            const otherBankInput = document.querySelector('input[name="other_bank_name"]');
            // 初始化總行下拉
            fetch('/api/bank_heads')
                .then(res => res.json())
                .then(heads => {
                    for (const [code, name] of Object.entries(heads)) {
                        bankSelect.innerHTML += `<option value="${code}">${code} - ${name}</option>`;
                    }
                    // 預設選中原本的銀行
                    bankSelect.value = "{{ account.bank_name }}";
                    bankSelect.dispatchEvent(new Event('change'));
                });
            // 當總行選擇改變時，載入分行
            bankSelect.addEventListener('change', function () {
                const headCode = this.value;
                branchSelect.innerHTML = '<option value="">請選擇分行</option>';
                if (!headCode) return;
                fetch('/api/bank_branches/' + headCode)
                    .then(res => res.json())
                    .then(branches => {
                        for (const branch of branches) {
                            branchSelect.innerHTML += `<option value="${branch.code}">${branch.code} - ${branch.name}</option>`;
                        }
                        // 預設選中原本的分行
                        branchSelect.value = "{{ account.branch }}";
                    });
            });
            // 其他銀行欄位顯示控制
            function toggleOtherBankField() {
                if (bankSelect.value === '其他') {
                    otherBankField.style.display = 'block';
                    otherBankInput.required = true;
                } else {
                    otherBankField.style.display = 'none';
                    otherBankInput.required = false;
                }
            }
            toggleOtherBankField();
            bankSelect.addEventListener('change', toggleOtherBankField);
            // 表單提交前處理
            document.querySelector('form').addEventListener('submit', function (e) {
                if (bankSelect.value === '其他') {
                    bankSelect.value = otherBankInput.value;
                }
            });
        });

        function toggleAmountEdit() {
            const input = document.getElementById('init_amount');
            const btn = document.getElementById('editAmountBtn');
            if (input.readOnly) {
                input.readOnly = false;
                input.classList.remove('is-static');
                btn.textContent = '完成';
                input.focus();
            } else {
                input.readOnly = true;
                btn.textContent = '修改';
            }
        }
        function showFileName(input) {
            document.getElementById('file-name').textContent = input.files[0]?.name || '';
        }
    </script>
</body>

</html>