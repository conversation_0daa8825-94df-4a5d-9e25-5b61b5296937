<!DOCTYPE html>
<html>
<head>
    <title>表單調試測試</title>
</head>
<body>
    <h1>簡單表單測試</h1>
    <form action="/expense_record" method="post">
        <p>記帳時間: <input type="date" name="a_time" value="2025-01-07"></p>
        <p>名稱: <input type="text" name="name" value="測試支出"></p>
        <p>總計: <input type="number" name="total" value="1000"></p>
        <p>科目代碼: <input type="text" name="subject_code" value="6020"></p>
        <p>帳戶ID: <input type="text" name="account_id" value="1"></p>
        <p>收付款狀態: <input type="hidden" name="is_paid" value="0"></p>
        <p><button type="submit">提交測試</button></p>
    </form>
    
    <script>
    document.querySelector('form').addEventListener('submit', function(e) {
        console.log('表單提交事件觸發');
        console.log('表單數據:', new FormData(this));
    });
    </script>
</body>
</html>