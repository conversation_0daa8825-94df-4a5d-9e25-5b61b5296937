<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>新增預付費用</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f5f6fa;
        }

        .column.is-narrow {
            flex: none;
            width: 200px !important;
            max-width: 200px !important;
            min-width: 200px !important;
        }

        .sidebar {
            max-width: 200px;
            min-width: 180px;
            width: 200px;
        }

        .sidebar .menu-label {
            font-size: 14px;
            font-weight: bold;
        }

        .sidebar .menu-list a {
            font-size: 20px;
            padding: 0.5em 0.75em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .form-section {
            background: #fff;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px #eee;
        }

        .main-content {
            flex: 1;
        }

        .is-flex {
            display: flex;
        }

        .is-align-items-flex-start {
            align-items: flex-start;
        }
    </style>
</head>

<body style="background:#f5f6fa;">
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <h1 class="title is-4 mb-4">
                    <a href="/?main=資產管理"
                        style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                    新增預付費用
                </h1>
                <form method="post" class="form-section">
                    <!-- 第一行 -->
                    <div class="columns">
                        <div class="column is-2">
                            <label class="label">記帳日期</label>
                            <div class="control">
                                <input class="input" type="date" name="record_date" required>
                            </div>
                        </div>
                        <div class="column is-2">
                            <label class="label">總金額(含稅)</label>
                            <div class="control">
                                <input class="input" type="number" name="amount" placeholder="請輸入總金額" required>
                            </div>
                        </div>
                        <div class="column is-2">
                            <label class="label">名稱</label>
                            <div class="control">
                                <input class="input" type="text" name="name" placeholder="請輸入名稱">
                            </div>
                        </div>
                        <div class="column is-2">
                            <label class="label">科目</label>
                            <div class="control">
                                <div class="select is-fullwidth">
                                    <select name="subject">
                                        <option value="">請選擇科目</option>
                                        {% for subject in subjects %}
                                        <option value="{{ subject.id }}">{{ subject.code }} {{ subject.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="column is-2">
                            <label class="label">資金帳戶</label>
                            <div class="control">
                                <div class="select is-fullwidth">
                                    <select name="account">
                                        <option value="">請選擇帳戶</option>
                                        {% for account in accounts %}
                                        <option value="{{ account.id }}">{{ account.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="column is-2">
                            <label class="label">收支對象</label>
                            <div class="control">
                                <div class="select is-fullwidth">
                                    <select name="target">
                                        <option value="">請選擇收支對象</option>
                                        {% for identity in identities %}
                                        <option value="{{ identity.id }}">{{ identity.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 第二行 -->
                    <div class="columns">
                        <div class="column is-2">
                            <label class="label">使用年數</label>
                            <div class="control">
                                <input class="input" type="number" name="years" min="0" value="1">
                            </div>
                        </div>
                        <div class="column is-2">
                            <label class="label">月</label>
                            <div class="control">
                                <div class="select is-fullwidth">
                                    <select name="months">
                                        {% for m in range(0, 13) %}
                                        <option value="{{ m }}">{{ m }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="column is-4">
                            <label class="label">起算日</label>
                            <div class="control">
                                <input class="input" type="date" name="start_date">
                            </div>
                        </div>
                        <div class="column is-2">
                            <label class="label">實付日期</label>
                            <div class="control">
                                <input class="input" type="date" name="paid_date">
                            </div>
                        </div>
                        <div class="column is-2">
                            <label class="label">應付日期</label>
                            <div class="control">
                                <input class="input" type="date" name="due_date">
                            </div>
                        </div>
                    </div>
                    <div class="field is-grouped is-grouped-right mt-5">
                        <div class="control">
                            <button class="button is-link" type="submit">儲存</button>
                        </div>
                        <div class="control">
                            <a class="button is-light" href="/">取消</a>
                        </div>
                    </div>
                    <!-- 認列紀錄區塊 -->
                    <div id="recognition-record" class="mt-5"></div>
                </form>
            </div>
            <div class="column is-4">
                <div class="box mb-4" style="width: 100%; max-height: 320px; overflow: auto;">
                    {% include 'voucher_section.html' %}
                </div>
                <div class="box" style="width: 100%; min-height: 100px; overflow: auto;">
                    {% include 'more_section.html' %}
                </div>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 憑證和更多功能的 JavaScript 已移至各自的模組中

            // 認列紀錄動態產生
            function getMonthEnd(date) {
                // 取得該月最後一天
                let d = new Date(date.getFullYear(), date.getMonth() + 1, 0);
                return d;
            }
            function pad(n) { return n < 10 ? '0' + n : n; }
            function formatDate(d) {
                return d.getFullYear() + '-' + pad(d.getMonth() + 1) + '-' + pad(d.getDate());
            }
            function renderRecognitionTable() {
                const amount = parseFloat(document.querySelector('input[name="amount"]').value) || 0;
                const years = parseInt(document.querySelector('input[name="years"]').value) || 0;
                const months = parseInt(document.querySelector('select[name="months"]').value) || 0;
                const startDateStr = document.querySelector('input[name="start_date"]').value;
                const totalMonths = years * 12 + months;
                const container = document.getElementById('recognition-record');
                if (!amount || !totalMonths || !startDateStr) {
                    container.innerHTML = '';
                    return;
                }
                let startDate = new Date(startDateStr);
                let rows = [];
                let perMonth = Math.floor((amount / totalMonths) * 100) / 100; // 小數點2位
                let remain = amount;
                for (let i = 0; i < totalMonths; i++) {
                    let d = new Date(startDate.getFullYear(), startDate.getMonth() + i, 1);
                    let endOfMonth = getMonthEnd(d);
                    let thisAmount = (i === totalMonths - 1) ? remain : perMonth;
                    remain -= thisAmount;
                    rows.push(`<tr><td>${formatDate(endOfMonth)}</td><td>${thisAmount} 元</td></tr>`);
                }
                let table = `
        <div class="box">
            <b>認列紀錄(預設每月月底)</b>
            <table class="table is-fullwidth is-bordered mt-2">
                <thead><tr><th>認列日期</th><th>金額(未稅)</th></tr></thead>
                <tbody>
                    ${rows.join('')}
                </tbody>
                <tfoot><tr><td>餘額(未稅)</td><td>${amount} 元</td></tr></tfoot>
            </table>
        </div>`;
                container.innerHTML = table;
            }
            // 監聽欄位變動
            ['amount', 'years', 'months', 'start_date'].forEach(name => {
                let el = document.querySelector(`[name="${name}"]`);
                if (el) el.addEventListener('input', renderRecognitionTable);
                if (el && el.tagName === 'SELECT') el.addEventListener('change', renderRecognitionTable);
            });
            renderRecognitionTable(); // 頁面載入時也跑一次
        });
    </script>
</body>

</html>