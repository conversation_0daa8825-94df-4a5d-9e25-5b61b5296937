<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>應收應付逾期</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        .main-title {
            font-size: 2.2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
        }

        .box-shadow {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .table thead {
            background: #2563eb;
            color: #fff;
        }

        .table th,
        .table td {
            vertical-align: middle;
            text-align: center;
        }

        .is-rounded-input {
            border-radius: 999px;
        }

        .is-search-input {
            border-radius: 999px;
            border: 1px solid #e5e7eb;
            padding-left: 1.5em;
        }

        .is-search-input:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 1.5px #2563eb33;
        }

        .no-data-img {
            width: 180px;
            margin-bottom: 1rem;
        }

        .no-data-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 260px;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=收支帳簿"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        應收應付逾期
                    </h1>
                </div>
                <div class="mb-4 is-flex is-align-items-center">
                    <div class="ml-auto">
                        <button class="button is-link is-light is-rounded">頁面指南</button>
                        <a class="button is-link is-rounded ml-2" href="/?main=收支帳簿&submenu=建立交易&button=新增帳務">＋建立帳務</a>
                    </div>
                </div>
                <div class="box box-shadow">
                    <div class="field is-grouped is-align-items-center mb-3">
                        <div class="control">
                            <div class="select is-rounded">
                                <select>
                                    <option>全部選取</option>
                                </select>
                            </div>
                        </div>
                        <div class="control ml-3">
                            <input class="input is-search-input" type="text" placeholder="輸入搜尋條件" style="width: 180px;">
                        </div>
                    </div>
                    <div class="table-container">
                        {% if overdue_records %}
                        <table class="table is-fullwidth is-hoverable">
                            <thead>
                                <tr>
                                    <th>記帳時間</th>
                                    <th>類型</th>
                                    <th>總計(含稅)</th>
                                    <th>科目</th>
                                    <th>名稱</th>
                                    <th>收支對象</th>
                                    <th>資金帳戶</th>
                                    <th>收付款狀態</th>
                                    <th>應收付日期</th>
                                    <th>逾期天數</th>
                                    <th>實收付日期</th>
                                    <th>發票日期</th>
                                    <th>備註</th>
                                    <th>編輯</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in overdue_records %}
                                <tr>
                                    <td>{{ record.a_time }}</td>
                                    <td>
                                        <span
                                            class="tag {% if record.money_type == '收入' %}is-success{% else %}is-danger{% endif %}">
                                            {{ record.money_type }}
                                        </span>
                                    </td>
                                    <td>{{ record.total }}</td>
                                    <td>{{ record.subject_name }}</td>
                                    <td>{{ record.name }}</td>
                                    <td>{{ record.payment_identity_name }}</td>
                                    <td>{{ record.account_name }}</td>
                                    <td>
                                        <span class="tag is-warning">{{ record.payment_status }}</span>
                                    </td>
                                    <td>{{ record.should_paid_date }}</td>
                                    <td>
                                        <span class="tag is-danger">{{ record.overdue_days }}天</span>
                                    </td>
                                    <td>{{ record.paid_date or '-' }}</td>
                                    <td>{{ record.invoice_date or '-' }}</td>
                                    <td>{{ record.note or '-' }}</td>
                                    <td>
                                        <a href="/{% if record.money_type == '收入' %}income_record{% else %}expense_record{% endif %}?edit={{ record.id }}"
                                            class="button is-small is-link">
                                            <span class="icon is-small">
                                                <i class="fas fa-edit"></i>
                                            </span>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                        {% else %}
                        <div class="no-data-box">
                            <img src="https://cdn-icons-png.flaticon.com/512/4076/4076549.png" class="no-data-img"
                                alt="no data">
                            <div class="has-text-grey">目前沒有逾期未收付款的記錄！</div>
                        </div>
                        {% endif %}
                    </div>
                    <div class="is-flex is-justify-content-flex-end is-align-items-center mt-2">
                        <span>共 1 頁　{{ overdue_records|length }}筆紀錄</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 搜尋功能
            const searchInput = document.querySelector('input[placeholder="輸入搜尋條件"]');
            if (searchInput) {
                searchInput.addEventListener('input', function () {
                    const searchTerm = this.value.toLowerCase();
                    const rows = document.querySelectorAll('tbody tr');

                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        if (text.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            }
        });
    </script>
</body>

</html>