<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>部門管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        .main-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
        }

        .department-table th {
            background: #2563eb !important;
            color: #fff !important;
            text-align: center;
            font-weight: 600 !important;
            border-color: #2563eb !important;
        }

        .department-table td {
            text-align: center;
            background: #f9fafb;
        }

        .department-table .edit-btn {
            color: #2563eb;
            background: none;
            border: none;
            font-size: 1.2em;
            cursor: pointer;
        }

        .department-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 0;
        }

        .right-panel {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 2rem;
        }

        .page-btns {
            float: right;
        }

        .pagination {
            justify-content: center;
        }

        .search-label {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 0.5em;
        }

        .search-box {
            border: 2px solid #2563eb;
            border-radius: 12px;
            padding: 1.2em 1.5em 0.5em 1.5em;
            margin-bottom: 1.5em;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=設定"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        部門管理
                    </h1>
                </div>
                <div class="mb-4">
                    <div class="page-btns" style="float:right;">
                        <button class="button is-link is-light mr-2"><span class="icon"><i
                                    class="fas fa-book"></i></span>頁面指南</button>
                        <a class="button is-link" href="{{ url_for('settings.department_create') }}">新增部門</a>
                    </div>
                </div>
                <div class="department-card p-0">
                    <table class="table department-table is-fullwidth mt-0 mb-0">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>上層部門名稱</th>
                                <th>部門名稱</th>
                                <th>備註</th>
                                <th>編輯</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for dept in departments %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ dept.parent_name }}</td>
                                <td>{{ dept.name }}</td>
                                <td>{{ dept.note }}</td>
                                <td>
                                    <button class="edit-btn" title="編輯">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="has-text-centered">目前沒有部門資料</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <nav class="pagination is-centered mt-4" role="navigation" aria-label="pagination">
                    <a class="pagination-previous" disabled><span class="icon"><i
                                class="fas fa-angle-left"></i></span></a>
                    <ul class="pagination-list">
                        <li><a class="pagination-link is-current">1</a></li>
                    </ul>
                    <a class="pagination-next" disabled><span class="icon"><i class="fas fa-angle-right"></i></span></a>
                </nav>
                <div class="has-text-right mt-2 mb-5">共1頁　0筆紀錄</div>
            </div>
            <div class="column is-3">
                <div class="right-panel">
                    <div class="search-label">查詢條件</div>
                    <div class="field">
                        <label class="label">部門名稱：</label>
                        <div class="control">
                            <input class="input" type="text" placeholder="請輸入部門名稱">
                        </div>
                    </div>
                    <div class="field is-grouped mt-4">
                        <div class="control">
                            <button class="button is-light">清空條件</button>
                        </div>
                        <div class="control">
                            <button class="button is-link">查詢</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</body>

</html>