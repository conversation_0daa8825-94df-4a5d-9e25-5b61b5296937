<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ account_type }}帳戶</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .container-box {
            max-width: 1400px;
            margin: 40px auto;
        }

        .main-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 2.5rem 2rem;
            max-width: 1400px;
            margin: 2rem auto;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 1.2rem;
        }

        .add-btn {
            float: right;
            margin-top: -0.5em;
        }

        .table th,
        .table td {
            vertical-align: middle;
        }

        .table thead th {
            background-color: #3273dc !important;
            color: white !important;
            font-weight: 600 !important;
            border-color: #3273dc !important;
        }

        .back-btn {
            margin-bottom: 1rem;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/account_setting"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        {{ account_type }}帳戶
                    </h1>
                </div>
                <div class="main-card">
                    <!-- Flash 訊息 -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                    {% for category, message in messages %}
                    <div
                        class="notification is-{{ 'success' if category == 'success' else 'danger' if category == 'error' else 'info' }} is-light">
                        <button class="delete" onclick="this.parentElement.remove()"></button>
                        {{ message }}
                    </div>
                    {% endfor %}
                    {% endif %}
                    {% endwith %}

                    <div class="section-title mb-5">
                        帳戶管理
                        {% if account_type == '現金' %}
                        <a href="/account/add/cash" class="button is-link is-small add-btn">＋ 新增現金帳戶</a>
                        {% else %}
                        <a href="/account/add/bank" class="button is-link is-small add-btn">＋ 新增銀行帳戶</a>
                        {% endif %}
                    </div>

                    <table class="table is-fullwidth is-striped" style="width:100%;">
                        <thead>
                            <tr>
                                <th>帳戶名稱</th>
                                {% if account_type == '銀行帳戶' %}
                                <th>銀行名稱</th>
                                <th>分行</th>
                                <th>帳號</th>
                                <th>戶名</th>
                                <th>科目代碼</th>
                                {% else %}
                                <th>期初金額</th>
                                <th>科目代碼</th>
                                <th>預設帳戶</th>
                                {% endif %}
                                <th>備註</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for acc in accounts %}
                            <tr{% if acc.is_default %} style="background-color: #e3f0fb;" {% endif %}>
                                <td>{{ acc.name }}</td>
                                {% if account_type == '銀行帳戶' %}
                                <td>{{ acc.bank_name or '' }}</td>
                                <td>{{ acc.branch or '' }}</td>
                                <td>{{ acc.account_number or '' }}</td>
                                <td>{{ acc.account_holder or '' }}</td>
                                <td>{{ acc.subject_code or '' }}</td>
                                {% else %}
                                <td>{{ acc.init_amount or '' }}</td>
                                <td>{{ acc.subject_code or '' }}</td>
                                <td>{% if acc.is_default %}✔{% endif %}</td>
                                {% endif %}
                                <td>{{ acc.note or '' }}</td>
                                <td>
                                    <a href="/account/edit/{{ acc.id }}" class="button is-small is-link">編輯</a>
                                    <form method="post" action="/account/delete/{{ acc.id }}" style="display:inline;">
                                        <button type="submit" class="button is-small is-danger"
                                            onclick="return confirm('確定要刪除嗎？')">刪除</button>
                                    </form>
                                </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="{% if account_type == '銀行帳戶' %}6{% else %}3{% endif %}"
                                        class="has-text-centered">
                                        尚無{{ account_type }}資料
                                    </td>
                                </tr>
                                {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 刪除確認對話框 -->
    <div id="deleteModal" class="modal">
        <div class="modal-background"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">確認刪除</p>
                <button class="delete" aria-label="close" onclick="closeDeleteModal()"></button>
            </header>
            <section class="modal-card-body">
                <p>確定要刪除這個帳戶嗎？此操作無法復原。</p>
            </section>
            <footer class="modal-card-foot">
                <button class="button is-danger" onclick="confirmDelete()">刪除</button>
                <button class="button" onclick="closeDeleteModal()">取消</button>
            </footer>
        </div>
    </div>

    <script>
        let accountToDelete = null;

        function deleteAccount(accountId) {
            accountToDelete = accountId;
            document.getElementById('deleteModal').classList.add('is-active');
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.remove('is-active');
            accountToDelete = null;
        }

        function confirmDelete() {
            if (accountToDelete) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/account/delete/${accountToDelete}`;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 點擊背景關閉對話框
        document.addEventListener('DOMContentLoaded', function () {
            const modal = document.getElementById('deleteModal');
            modal.querySelector('.modal-background').addEventListener('click', closeDeleteModal);
        });
    </script>
</body>

</html>