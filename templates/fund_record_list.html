<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>資金紀錄列表</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
    }

    .column.is-narrow {
      flex: none;
      width: 200px !important;
      max-width: 200px !important;
      min-width: 200px !important;
    }

    .sidebar {
      max-width: 200px;
      min-width: 180px;
      width: 200px;
    }

    .sidebar .menu-label {
      font-size: 14px;
      font-weight: bold;
    }

    .sidebar .menu-list a {
      font-size: 20px;
      padding: 0.5em 0.75em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .main-content {
      padding: 2rem 1rem;
    }

    .header-section {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;
    }

    .back-link {
      font-size: 1.2rem;
      color: #363636;
      text-decoration: none;
      margin-right: 1rem;
    }

    .back-link:hover {
      color: #3273dc;
    }

    .header-buttons {
      margin-left: auto;
      display: flex;
      gap: 0.5rem;
    }

    .table-container {
      background: white;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
    }

    .table {
      margin-bottom: 0;
    }

    .table thead th {
      background-color: #3273dc !important;
      color: white !important;
      font-weight: 600 !important;
      border: none !important;
      border-color: #3273dc !important;
      padding: 1rem 0.75rem;
      text-align: center;
      vertical-align: middle;
    }

    .table td {
      padding: 0.75rem;
      text-align: center;
      vertical-align: middle;
      border-bottom: 1px solid #dbdbdb;
    }

    .table tbody tr:hover {
      background-color: #fafafa;
    }

    .action-buttons {
      display: flex;
      gap: 0.25rem;
      justify-content: center;
    }

    .action-buttons .button {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }

    .status-badge {
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.75rem;
      font-weight: 600;
    }

    .status-income {
      background-color: #48c774;
      color: white;
    }

    .status-expense {
      background-color: #f14668;
      color: white;
    }
  </style>
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <!-- 頁面標題 -->
        <div class="mb-4">
          <h1 class="title is-4">
            <a href="/?main=資金管理"
              style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
            資金紀錄列表
          </h1>
        </div>
        <div class="main-content">
          <div class="header-section">
            <div class="header-buttons">
              <button class="button is-info is-light">頁面指南</button>
              <a href="/fund_record/create" class="button is-link">新增資金紀錄</a>
            </div>
          </div>

          <div class="table-container">
            <table class="table is-fullwidth">
              <thead>
                <tr>
                  <th>記帳日期</th>
                  <th>資金類型</th>
                  <th>收支類型</th>
                  <th>金額</th>
                  <th>帳戶</th>
                  <th>會計科目</th>
                  <th>收支對象</th>
                  <th>部門</th>
                  <th>專案</th>
                  <th>備註</th>
                  <th>憑證</th>
                  <th>動作</th>
                </tr>
              </thead>
              <tbody>
                {% if fund_records %}
                  {% for record in fund_records %}
                  <tr>
                    <td>{{ record.record_date or '-' }}</td>
                    <td>{{ record.fund_type or '-' }}</td>
                    <td>
                      {% if record.money_type == '收入' %}
                        <span class="status-badge status-income">收入</span>
                      {% elif record.money_type == '支出' %}
                        <span class="status-badge status-expense">支出</span>
                      {% else %}
                        -
                      {% endif %}
                    </td>
                    <td>{{ record.amount or '0' }}</td>
                    <td>{{ record.account_name or '-' }}</td>
                    <td>{{ record.subject_name or '-' }}</td>
                    <td>{{ record.payment_identity_name or '-' }}</td>
                    <td>{{ record.department_name or '-' }}</td>
                    <td>{{ record.project_name or '-' }}</td>
                    <td>{{ record.note or '-' }}</td>
                    <td>
                      {% if record.voucher_path %}
                        <a href="{{ record.voucher_path }}" target="_blank" class="button is-small is-info is-light">查看</a>
                      {% else %}
                        -
                      {% endif %}
                    </td>
                    <td>
                      <div class="action-buttons">
                        <a href="/fund_record/edit/{{ record.id }}" class="button is-small is-warning is-light">編輯</a>
                        <button class="button is-small is-danger is-light" onclick="deleteRecord({{ record.id }})">刪除</button>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td colspan="12" class="has-text-grey">目前沒有資金紀錄</td>
                  </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
  <script>
    function deleteRecord(id) {
      if (confirm('確定要刪除這筆資金紀錄嗎？')) {
        fetch(`/fund_record/delete/${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          }
        })
        .then(response => {
          if (response.ok) {
            location.reload();
          } else {
            alert('刪除失敗，請稍後再試');
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('刪除失敗，請稍後再試');
        });
      }
    }
  </script>
</body>

</html>
