<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系統日誌查看</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        .log-content {
            background-color: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            padding: 1rem;
            border-radius: 5px;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .log-controls {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <nav class="navbar is-dark" role="navigation">
        <div class="navbar-brand">
            <a class="navbar-item" href="/">
                <strong>會計系統 - 日誌查看</strong>
            </a>
        </div>
        <div class="navbar-menu">
            <div class="navbar-end">
                <a class="navbar-item" href="/admin/monitoring">監控儀表板</a>
                <a class="navbar-item" href="/admin/monitoring/health">健康檢查</a>
            </div>
        </div>
    </nav>

    <section class="section">
        <div class="container">
            <h1 class="title">系統日誌查看</h1>
            
            <div class="log-controls">
                <div class="field is-grouped">
                    <div class="control">
                        <div class="select">
                            <select id="logType" onchange="changeLogType()">
                                {% for log in log_files %}
                                <option value="{{ log.type }}" {% if log.type == current_log %}selected{% endif %}>
                                    {{ log.name }} ({{ log.size_mb }} MB)
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="control">
                        <div class="select">
                            <select id="lineCount" onchange="changeLineCount()">
                                <option value="50" {% if lines == 50 %}selected{% endif %}>最近 50 行</option>
                                <option value="100" {% if lines == 100 %}selected{% endif %}>最近 100 行</option>
                                <option value="200" {% if lines == 200 %}selected{% endif %}>最近 200 行</option>
                                <option value="500" {% if lines == 500 %}selected{% endif %}>最近 500 行</option>
                            </select>
                        </div>
                    </div>
                    <div class="control">
                        <button class="button is-primary" onclick="refreshLogs()">
                            <span class="icon">
                                <i class="fas fa-sync-alt"></i>
                            </span>
                            <span>刷新</span>
                        </button>
                    </div>
                    <div class="control">
                        <button class="button is-info" onclick="toggleAutoRefresh()">
                            <span id="autoRefreshText">啟用自動刷新</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="box">
                <h2 class="subtitle">{{ current_log }}.log</h2>
                <div class="log-content" id="logContent">{{ log_content }}</div>
            </div>
        </div>
    </section>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        function changeLogType() {
            const logType = document.getElementById('logType').value;
            const lineCount = document.getElementById('lineCount').value;
            window.location.href = `/admin/monitoring/logs?type=${logType}&lines=${lineCount}`;
        }

        function changeLineCount() {
            const logType = document.getElementById('logType').value;
            const lineCount = document.getElementById('lineCount').value;
            window.location.href = `/admin/monitoring/logs?type=${logType}&lines=${lineCount}`;
        }

        async function refreshLogs() {
            try {
                const logType = document.getElementById('logType').value;
                const lineCount = document.getElementById('lineCount').value;
                
                const response = await fetch(`/admin/monitoring/api/logs?type=${logType}&lines=${lineCount}`);
                const data = await response.json();
                
                document.getElementById('logContent').textContent = data.content;
                
                // 自動滾動到底部
                const logContent = document.getElementById('logContent');
                logContent.scrollTop = logContent.scrollHeight;
                
            } catch (error) {
                console.error('刷新日誌失敗:', error);
            }
        }

        function toggleAutoRefresh() {
            const button = document.getElementById('autoRefreshText');
            
            if (isAutoRefresh) {
                // 停止自動刷新
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                isAutoRefresh = false;
                button.textContent = '啟用自動刷新';
            } else {
                // 開始自動刷新
                autoRefreshInterval = setInterval(refreshLogs, 5000); // 每5秒刷新
                isAutoRefresh = true;
                button.textContent = '停止自動刷新';
            }
        }

        // 頁面載入時滾動到底部
        document.addEventListener('DOMContentLoaded', function() {
            const logContent = document.getElementById('logContent');
            logContent.scrollTop = logContent.scrollHeight;
        });
    </script>
</body>
</html>