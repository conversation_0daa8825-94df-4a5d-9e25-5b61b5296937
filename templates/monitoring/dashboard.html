<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系統監控儀表板</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        .status-healthy { color: #48c774; }
        .status-warning { color: #ffdd57; }
        .status-error { color: #f14668; }
        .table thead th {
            background-color: #3273dc !important;
            color: white !important;
            font-weight: 600 !important;
            border-color: #3273dc !important;
        }
    </style>
</head>
<body>
    <nav class="navbar is-dark" role="navigation">
        <div class="navbar-brand">
            <a class="navbar-item" href="/">
                <strong>會計系統 - 系統監控</strong>
            </a>
        </div>
        <div class="navbar-menu">
            <div class="navbar-end">
                <a class="navbar-item" href="/admin/monitoring/logs">查看日誌</a>
                <a class="navbar-item" href="/admin/monitoring/health">健康檢查</a>
            </div>
        </div>
    </nav>

    <section class="section">
        <div class="container">
            <h1 class="title">系統監控儀表板</h1>
            
            <!-- 系統資源概覽 -->
            <div class="columns">
                <div class="column is-3">
                    <div class="metric-card">
                        <div class="metric-value" id="cpu-usage">{{ "%.1f"|format(system_info.cpu_percent) }}%</div>
                        <div class="metric-label">CPU 使用率</div>
                    </div>
                </div>
                <div class="column is-3">
                    <div class="metric-card">
                        <div class="metric-value" id="memory-usage">{{ "%.1f"|format(system_info.memory.percent) }}%</div>
                        <div class="metric-label">記憶體使用率</div>
                    </div>
                </div>
                <div class="column is-3">
                    <div class="metric-card">
                        <div class="metric-value" id="disk-usage">{{ "%.1f"|format((system_info.disk.used / system_info.disk.total * 100)) }}%</div>
                        <div class="metric-label">磁碟使用率</div>
                    </div>
                </div>
                <div class="column is-3">
                    <div class="metric-card">
                        <div class="metric-value" id="uptime">{{ "%.0f"|format((perf_stats.summary.uptime / 3600)) }}h</div>
                        <div class="metric-label">系統運行時間</div>
                    </div>
                </div>
            </div>

            <!-- 性能統計 -->
            <div class="columns">
                <div class="column is-6">
                    <div class="box">
                        <h2 class="subtitle">請求統計</h2>
                        <div class="columns">
                            <div class="column">
                                <div class="has-text-centered">
                                    <p class="heading">總請求數</p>
                                    <p class="title is-4" id="total-requests">{{ perf_stats.summary.total_requests }}</p>
                                </div>
                            </div>
                            <div class="column">
                                <div class="has-text-centered">
                                    <p class="heading">慢請求</p>
                                    <p class="title is-4" id="slow-requests">{{ perf_stats.summary.slow_requests }}</p>
                                </div>
                            </div>
                            <div class="column">
                                <div class="has-text-centered">
                                    <p class="heading">平均響應時間</p>
                                    <p class="title is-4" id="avg-response">{{ "%.3f"|format(perf_stats.summary.avg_response_time) }}s</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="column is-6">
                    <div class="box">
                        <h2 class="subtitle">系統資源趨勢</h2>
                        <div class="chart-container">
                            <canvas id="systemResourceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近請求 -->
            <div class="box">
                <h2 class="subtitle">最近請求</h2>
                <div class="table-container">
                    <table class="table is-fullwidth is-striped">
                        <thead>
                            <tr>
                                <th>時間</th>
                                <th>端點</th>
                                <th>方法</th>
                                <th>響應時間</th>
                                <th>狀態碼</th>
                            </tr>
                        </thead>
                        <tbody id="recent-requests">
                            {% for req in perf_stats.recent_requests[-10:] %}
                            <tr>
                                <td>{{ req.time.strftime('%H:%M:%S') if req.time else 'N/A' }}</td>
                                <td>{{ req.endpoint }}</td>
                                <td>{{ req.method }}</td>
                                <td>{{ "%.3f"|format(req.response_time) }}s</td>
                                <td>
                                    <span class="tag {% if req.status_code < 400 %}is-success{% elif req.status_code < 500 %}is-warning{% else %}is-danger{% endif %}">
                                        {{ req.status_code }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 日誌文件 -->
            {% if log_files %}
            <div class="box">
                <h2 class="subtitle">日誌文件</h2>
                <div class="columns">
                    {% for log in log_files[:4] %}
                    <div class="column">
                        <div class="card">
                            <div class="card-content">
                                <p class="title is-6">{{ log.name }}</p>
                                <p class="subtitle is-7">{{ log.size_mb }} MB</p>
                                <p class="is-size-7">{{ log.modified.strftime('%Y-%m-%d %H:%M') }}</p>
                                <a href="/admin/monitoring/logs?type={{ log.type }}" class="button is-small is-primary">查看</a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </section>

    <!-- 刷新按鈕 -->
    <button class="button is-primary is-large refresh-btn" onclick="refreshData()">
        <span class="icon">
            <i class="fas fa-sync-alt"></i>
        </span>
        <span>刷新</span>
    </button>

    <script>
        // 初始化圖表
        const ctx = document.getElementById('systemResourceChart').getContext('2d');
        const systemResourceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU %',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.1
                }, {
                    label: '記憶體 %',
                    data: [],
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // 刷新數據
        async function refreshData() {
            try {
                const response = await fetch('/admin/monitoring/api/stats');
                const data = await response.json();
                
                // 更新指標卡片
                document.getElementById('cpu-usage').textContent = data.cpu_percent.toFixed(1) + '%';
                document.getElementById('memory-usage').textContent = data.memory_percent.toFixed(1) + '%';
                document.getElementById('disk-usage').textContent = data.disk_percent.toFixed(1) + '%';
                document.getElementById('total-requests').textContent = data.summary.total_requests;
                document.getElementById('slow-requests').textContent = data.summary.slow_requests;
                document.getElementById('avg-response').textContent = data.summary.avg_response_time.toFixed(3) + 's';
                
                // 更新圖表
                updateChart(data);
                
            } catch (error) {
                console.error('刷新數據失敗:', error);
            }
        }

        function updateChart(data) {
            const cpuData = data.system_metrics.cpu_usage || [];
            const memoryData = data.system_metrics.memory_usage || [];
            
            if (cpuData.length > 0) {
                const labels = cpuData.map(item => new Date(item.time).toLocaleTimeString());
                
                systemResourceChart.data.labels = labels;
                systemResourceChart.data.datasets[0].data = cpuData.map(item => item.percent);
                systemResourceChart.data.datasets[1].data = memoryData.map(item => item.percent);
                systemResourceChart.update();
            }
        }

        // 自動刷新
        setInterval(refreshData, 30000); // 每30秒刷新一次
        
        // 頁面載入時刷新一次
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(refreshData, 1000);
        });
    </script>
</body>
</html>