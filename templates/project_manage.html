<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>專案管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .main-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
        }

        .project-table th {
            background: #2563eb;
            color: #fff;
            text-align: center;
        }

        .project-table td {
            text-align: center;
            background: #f9fafb;
        }

        .project-table .edit-btn {
            color: #2563eb;
            background: none;
            border: none;
            font-size: 1.2em;
            cursor: pointer;
        }

        .project-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 0;
        }

        .right-panel {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 2rem;
        }

        .page-btns {
            float: right;
        }

        .pagination {
            justify-content: center;
        }

        .search-label {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 0.5em;
        }

        .search-box {
            border: 2px solid #2563eb;
            border-radius: 12px;
            padding: 1.2em 1.5em 0.5em 1.5em;
            margin-bottom: 1.5em;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 999px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-ongoing {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .status-completed {
            background: #dcfce7;
            color: #166534;
        }

        .status-paused {
            background: #fef3c7;
            color: #92400e;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=設定"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        專案管理
                    </h1>
                </div>
                <div class="mb-4">
                    <div class="page-btns" style="float:right;">
                        <button class="button is-link is-light mr-2"><span class="icon"><i
                                    class="fas fa-book"></i></span>頁面指南</button>
                        <a class="button is-link" href="{{ url_for('settings.project_create') }}">新增專案</a>
                    </div>
                </div>
                <div class="project-card p-0">
                    <table class="table project-table is-fullwidth mt-0 mb-0">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>專案代碼</th>
                                <th>專案名稱</th>
                                <th>狀態</th>
                                <th>負責部門</th>
                                <th>專案負責人</th>
                                <th>預算</th>
                                <th>開始日期</th>
                                <th>結束日期</th>
                                <th>備註</th>
                                <th>編輯</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for project in projects %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ project.code }}</td>
                                <td>{{ project.name }}</td>
                                <td>
                                    {% if project.status == '進行中' %}
                                    <span class="status-badge status-ongoing">{{ project.status }}</span>
                                    {% elif project.status == '已完成' %}
                                    <span class="status-badge status-completed">{{ project.status }}</span>
                                    {% elif project.status == '暫停' %}
                                    <span class="status-badge status-paused">{{ project.status }}</span>
                                    {% else %}
                                    <span class="status-badge">{{ project.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ project.department_name }}</td>
                                <td>{{ project.manager }}</td>
                                <td>{{ project.budget }}</td>
                                <td>{{ project.start_date }}</td>
                                <td>{{ project.end_date }}</td>
                                <td>{{ project.note }}</td>
                                <td>
                                    <a class="button is-small is-info"
                                        href="{{ url_for('settings.project_edit', project_id=project.id) }}" title="編輯">
                                        <span class="icon is-small">
                                            <i class="fas fa-edit"></i>
                                        </span>
                                        <span>編輯</span>
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="11" class="has-text-centered">目前沒有專案資料</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <nav class="pagination is-centered mt-4" role="navigation" aria-label="pagination">
                    <a class="pagination-previous" disabled><span class="icon"><i
                                class="fas fa-angle-left"></i></span></a>
                    <ul class="pagination-list">
                        <li><a class="pagination-link is-current">1</a></li>
                    </ul>
                    <a class="pagination-next" disabled><span class="icon"><i class="fas fa-angle-right"></i></span></a>
                </nav>
                <div class="has-text-right mt-2 mb-5">共1頁　{{ projects|length }}筆紀錄</div>
            </div>
            <div class="column is-3">
                <div class="right-panel">
                    <div class="search-label">查詢條件</div>
                    <div class="field">
                        <label class="label">專案名稱：</label>
                        <div class="control">
                            <input class="input" type="text" placeholder="請輸入專案名稱">
                        </div>
                    </div>
                    <div class="field">
                        <label class="label">專案狀態：</label>
                        <div class="control">
                            <div class="select is-fullwidth">
                                <select>
                                    <option value="">全部狀態</option>
                                    <option value="進行中">進行中</option>
                                    <option value="已完成">已完成</option>
                                    <option value="暫停">暫停</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="field is-grouped mt-4">
                        <div class="control">
                            <button class="button is-light">清空條件</button>
                        </div>
                        <div class="control">
                            <button class="button is-link">查詢</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>