<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>新增資金紀錄</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
    }

    .column.is-narrow {
      flex: none;
      width: 200px !important;
      max-width: 200px !important;
      min-width: 200px !important;
    }

    .sidebar {
      max-width: 200px;
      min-width: 180px;
      width: 200px;
    }

    .sidebar .menu-label {
      font-size: 14px;
      font-weight: bold;
    }

    .sidebar .menu-list a {
      font-size: 20px;
      padding: 0.5em 0.75em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .main-content {
      padding: 2rem 1rem;
    }

    .form-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .form-header {
      background: #3273dc;
      color: white;
      padding: 1rem 1.5rem;
      font-size: 1.2rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .back-link {
      color: white;
      text-decoration: none;
      font-size: 1.1rem;
    }

    .back-link:hover {
      color: #f0f0f0;
    }

    .form-content {
      padding: 2rem;
    }

    .field-group {
      margin-bottom: 1.5rem;
    }

    .field-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .field-row .field {
      flex: 1;
    }

    .upload-area {
      border: 2px dashed #dbdbdb;
      border-radius: 6px;
      padding: 2rem;
      text-align: center;
      cursor: pointer;
      transition: border-color 0.3s;
    }

    .upload-area:hover {
      border-color: #3273dc;
    }

    .upload-area .icon {
      font-size: 2rem;
      color: #b5b5b5;
      margin-bottom: 0.5rem;
    }

    .submit-section {
      text-align: center;
      padding-top: 1.5rem;
      border-top: 1px solid #dbdbdb;
      margin-top: 2rem;
    }

    .radio-group {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .select-full {
      width: 100%;
    }
  </style>
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <!-- 頁面標題 -->
        <div class="mb-4">
          <h1 class="title is-4">
            <a href="/?main=資金管理"
              style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
            新增資金紀錄
          </h1>
        </div>
        <div class="main-content">
          <div class="form-container">
            <div class="form-header">
              <button class="button is-info is-light">頁面指南</button>
            </div>
            
            <div class="form-content">
              <form method="POST" enctype="multipart/form-data">
                <!-- 基本資訊 -->
                <div class="field-group">
                  <div class="field-row">
                    <div class="field">
                      <label class="label">資金類型</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="fund_type" required>
                            <option value="">請選擇資金類型</option>
                            <option value="暫收款">暫收款</option>
                            <option value="暫付款">暫付款</option>
                            <option value="股東往來">股東往來</option>
                            <option value="存入保證金">存入保證金</option>
                            <option value="存出保證金">存出保證金</option>
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">收支類型</label>
                      <div class="control">
                        <div class="radio-group">
                          <label class="radio">
                            <input type="radio" name="money_type" value="收入" required>
                            收入
                          </label>
                          <label class="radio">
                            <input type="radio" name="money_type" value="支出" required>
                            支出
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 金額和日期 -->
                <div class="field-group">
                  <div class="field-row">
                    <div class="field">
                      <label class="label">金額 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <input class="input" type="number" name="amount" placeholder="請輸入金額" required>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">記帳日期 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <input class="input" type="date" name="record_date" required>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 帳戶和科目 -->
                <div class="field-group">
                  <div class="field-row">
                    <div class="field">
                      <label class="label">帳戶 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="account_id" required>
                            <option value="">請選擇帳戶</option>
                            <!-- 這裡可以從後端傳入帳戶資料 -->
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">會計科目</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="subject_code">
                            <option value="">請選擇會計科目</option>
                            <!-- 這裡可以從後端傳入科目資料 -->
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 對象和備註 -->
                <div class="field-group">
                  <div class="field-row">
                    <div class="field">
                      <label class="label">收支對象</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="payment_identity_id">
                            <option value="">請選擇收支對象</option>
                            <!-- 這裡可以從後端傳入收支對象資料 -->
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">部門</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="department_id">
                            <option value="">請選擇部門</option>
                            <!-- 這裡可以從後端傳入部門資料 -->
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 專案和標籤 -->
                <div class="field-group">
                  <div class="field-row">
                    <div class="field">
                      <label class="label">專案</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="project_id">
                            <option value="">請選擇專案</option>
                            <!-- 這裡可以從後端傳入專案資料 -->
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">標籤</label>
                      <div class="control">
                        <input class="input" type="text" name="tags" placeholder="請輸入標籤">
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 備註 -->
                <div class="field-group">
                  <div class="field">
                    <label class="label">備註</label>
                    <div class="control">
                      <textarea class="textarea" name="note" rows="3" placeholder="請輸入備註"></textarea>
                    </div>
                  </div>
                </div>

                <!-- 憑證上傳 -->
                <div class="field-group">
                  <div class="field">
                    <label class="label">憑證</label>
                    <div class="control">
                      <div class="upload-area" onclick="document.getElementById('voucher-file').click()">
                        <div class="icon">
                          <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <p>點擊上傳憑證</p>
                        <input type="file" id="voucher-file" name="voucher" style="display: none;" accept="image/*,.pdf">
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 提交按鈕 -->
                <div class="submit-section">
                  <button type="submit" class="button is-primary is-large">儲存</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
  <script>
    // 設定今天的日期為預設值
    document.addEventListener('DOMContentLoaded', function() {
      const today = new Date().toISOString().split('T')[0];
      document.querySelector('input[name="record_date"]').value = today;
    });

    // 檔案上傳處理
    document.getElementById('voucher-file').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        const uploadArea = document.querySelector('.upload-area p');
        uploadArea.textContent = `已選擇: ${file.name}`;
      }
    });
  </script>
</body>

</html>
