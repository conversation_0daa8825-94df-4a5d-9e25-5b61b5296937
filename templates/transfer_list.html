{# 資金移轉紀錄列表頁 #}
<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <title>資金移轉紀錄</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
    }

    .column.is-narrow {
      flex: none;
      width: 200px !important;
      max-width: 200px !important;
      min-width: 200px !important;
    }

    .sidebar {
      max-width: 200px;
      min-width: 180px;
      width: 200px;
    }

    .sidebar .menu-label {
      font-size: 14px;
      font-weight: bold;
    }

    .sidebar .menu-list a {
      font-size: 20px;
      padding: 0.5em 0.75em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .transfer-table th,
    .transfer-table td {
      text-align: center;
      vertical-align: middle;
    }

    .date-range {
      min-width: 260px;
    }

    .action-bar {
      display: flex;
      gap: 1rem;
      align-items: center;
      margin-bottom: 1rem;
    }
  </style>
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <!-- 頁面標題 -->
        <div class="mb-4">
          <h1 class="title is-4">
            <a href="/?main=資金管理"
              style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
            資金移轉紀錄
          </h1>
        </div>
        <div class="action-bar">
          <input class="input date-range" type="text" value="2025/06/10 - 2025/07/10" readonly>
          <button class="button is-info is-light">頁面指南</button>
          <a class="button is-link" href="/transfer">＋建立一筆新紀錄</a>
        </div>
        <table class="table is-fullwidth is-hoverable transfer-table">
          <thead>
            <tr class="has-background-link has-text-white">
              <th>轉出帳戶</th>
              <th>轉入帳戶</th>
              <th>總金額</th>
              <th>手續費</th>
              <th>轉移日期</th>
              <th>備註</th>
              <th>憑證圖檔</th>
              <th>編輯</th>
            </tr>
          </thead>
          <tbody>
            {% for row in transfer_records %}
            <tr>
              <td>{{ row.out_account.name if row.out_account else '-' }}</td>
              <td>{{ row.in_account.name if row.in_account else '-' }}</td>
              <td>${{ '{:,.0f}'.format(row.amount or 0) }}</td>
              <td>${{ '{:,.0f}'.format(row.fee or 0) }}</td>
              <td>{{ row.transfer_date or '-' }}</td>
              <td>{{ row.note or '-' }}</td>
              <td>
                {% if row.voucher %}
                  <a href="{{ row.voucher }}" target="_blank">查看</a>
                {% else %}-{% endif %}
              </td>
              <td>
                <a class="button is-small is-info" href="/transfer/edit/{{ row.id }}">編輯</a>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>

</html>