<!-- 憑證功能模組 -->
<div class="field">
    <label class="label">憑證取得</label>
    <div class="buttons has-addons" id="voucher-btns">
        <button class="button is-selected" type="button">憑證尚未取得</button>
        <button class="button" type="button">沒有憑證</button>
        <button class="button" type="button">有憑證</button>
    </div>
</div>

<div class="field" id="invoice-fields" style="display:none;">
    <div class="field mb-3">
        <label class="checkbox">
            <input type="checkbox" name="is_paper"> 收據類憑證（無發票號碼）
        </label>
    </div>
    <div class="columns is-multiline">
        <div class="column is-4">
            <div class="field">
                <label class="label">發票號碼</label>
                <div class="control is-flex is-align-items-center">
                    <input class="input" type="text" name="invoice_number" id="invoice-number-input" placeholder="請輸入發票號碼">
                    <span id="invoice-number-status" class="ml-2"></span>
                </div>
            </div>
        </div>
        <div class="column is-4">
            <div class="field">
                <label class="label">稅別</label>
                <div class="control">
                    <div class="select is-fullwidth">
                        <select name="tax_type">
                            <option value="應稅" selected>應稅</option>
                            <option value="免稅">免稅</option>
                            <option value="零稅率">零稅率</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="column is-4">
            <div class="field">
                <label class="label">發票日期</label>
                <div class="control">
                    <input class="input" type="date" name="invoice_date">
                </div>
            </div>
        </div>
    </div>
    <div class="columns is-multiline">
        <div class="column is-4">
            <div class="field">
                <label class="label">買方統編</label>
                <div class="control">
                    <input class="input" type="text" name="buyer_tax_id" placeholder="請輸入買方統編">
                </div>
            </div>
        </div>
        <div class="column is-4">
            <div class="field">
                <label class="label">賣方統編</label>
                <div class="control">
                    <input class="input" type="text" name="seller_tax_id" placeholder="請輸入賣方統編" value="{{ company_id }}">
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 憑證功能 JavaScript
(function() {
    // 憑證取得按鈕切換發票欄位顯示
    const voucherBtns = document.querySelectorAll('#voucher-btns button');
    const invoiceFields = document.getElementById('invoice-fields');
    
    voucherBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            voucherBtns.forEach(b => { 
                b.classList.remove('is-selected','is-primary'); 
            });
            this.classList.add('is-selected','is-primary');
            
            if (this.textContent.trim() === '有憑證') {
                invoiceFields.style.display = '';
            } else {
                invoiceFields.style.display = 'none';
            }
        });
    });

    // 發票號碼即時檢查功能
    const invoiceNumberInput = document.getElementById('invoice-number-input');
    const invoiceNumberStatus = document.getElementById('invoice-number-status');
    
    if (invoiceNumberInput && invoiceNumberStatus) {
        let lastValue = '';
        let timer = null;
        invoiceNumberInput.addEventListener('input', function() {
            const value = this.value.trim();
            if (value === lastValue) return;
            lastValue = value;
            invoiceNumberStatus.textContent = '';
            if (timer) clearTimeout(timer);
            if (!value) return;
            timer = setTimeout(() => {
                fetch(`/api/check_invoice_number?number=${encodeURIComponent(value)}`)
                    .then(r => r.json())
                    .then(data => {
                        if (data.exists) {
                            invoiceNumberStatus.textContent = '號碼已存在';
                            invoiceNumberStatus.style.color = 'red';
                        } else {
                            invoiceNumberStatus.textContent = '號碼正確';
                            invoiceNumberStatus.style.color = 'green';
                        }
                    })
                    .catch(() => {
                        invoiceNumberStatus.textContent = '查詢失敗';
                        invoiceNumberStatus.style.color = 'orange';
                    });
            }, 400);
        });
    }
})();
</script>