<!-- 更多功能模組 -->
<div class="field mt-4">
    <label class="label">更多　
        <input type="checkbox" class="switch is-rounded is-info" id="more-toggle">
        <span class="ml-2"></span>
    </label>
</div>

<div id="more-section" style="display:none;">
    <div class="columns is-mobile">
        <div class="column is-3">
            <div class="field">
                <label class="label">部門別
                    <span class="icon has-text-info" title="部門說明">
                        <i class="fas fa-question-circle"></i>
                    </span>
                </label>
                <div class="control">
                    <div class="select is-fullwidth">
                        <select name="department_id">
                            <option value="">選擇部門</option>
                            {% for dept in departments %}
                                <option value="{{ dept.id }}">{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="column is-3">
            <div class="field">
                <label class="label">專案別
                    <span class="icon has-text-info" title="專案說明">
                        <i class="fas fa-question-circle"></i>
                    </span>
                </label>
                <div class="control">
                    <div class="select is-fullwidth">
                        <select name="project_id">
                            <option value="">選擇專案</option>
                            {% for proj in projects %}
                                <option value="{{ proj.id }}">{{ proj.code }} - {{ proj.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="column is-3">
            <div class="field">
                <label class="label">標籤</label>
                <div class="control">
                    <input class="input" type="text" name="tag" placeholder="輸入標籤">
                </div>
            </div>
        </div>
        <div class="column is-3">
            <div class="field">
                <label class="label">備註</label>
                <div class="control">
                    <textarea class="textarea" name="note" placeholder="請輸入備註" rows="2"></textarea>
                </div>
            </div>
        </div>
    </div>
    
    <div class="field">
        <label class="label">其他檔案</label>
        <div class="control">
            <input type="file" name="other_file" class="is-hidden" id="other-file-input" accept="image/*,.pdf,.doc,.docx">
            <label for="other-file-input" class="button is-outlined is-info">
                <span class="icon"><i class="fas fa-upload"></i></span>
                <span>上傳檔案</span>
            </label>
            <span id="file-name" class="ml-2 has-text-grey"></span>
        </div>
        <p class="help">支援圖片、PDF、Word 文件</p>
    </div>
</div>

<script>
// 更多功能 JavaScript
(function() {
    // 更多功能切換
    const moreToggle = document.getElementById('more-toggle');
    const moreSection = document.getElementById('more-section');
    
    if (moreToggle) {
        moreToggle.addEventListener('change', function() {
            moreSection.style.display = this.checked ? '' : 'none';
        });
    }

    // 檔案上傳顯示檔名
    const fileInput = document.getElementById('other-file-input');
    const fileName = document.getElementById('file-name');
    
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                fileName.textContent = this.files[0].name;
            } else {
                fileName.textContent = '';
            }
        });
    }
})();
</script>