<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>投資管理</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
    }

    .column.is-narrow {
      flex: none;
      width: 200px !important;
      max-width: 200px !important;
      min-width: 200px !important;
    }

    .sidebar {
      max-width: 200px;
      min-width: 180px;
      width: 200px;
    }

    .sidebar .menu-label {
      font-size: 14px;
      font-weight: bold;
    }

    .sidebar .menu-list a {
      font-size: 20px;
      padding: 0.5em 0.75em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .main-content {
      padding: 2rem 1rem;
    }

    .header-section {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;
    }

    .back-link {
      font-size: 1.5rem;
      color: #363636;
      text-decoration: none;
      margin-right: 1rem;
      font-weight: 600;
    }

    .back-link:hover {
      color: #3273dc;
    }

    .header-buttons {
      margin-left: auto;
      display: flex;
      gap: 0.5rem;
    }

    .search-section {
      background: white;
      padding: 1rem;
      border-radius: 6px;
      margin-bottom: 1rem;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .search-section .field {
      margin-bottom: 0;
    }

    .table-container {
      background: white;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
    }

    .table {
      margin-bottom: 0;
    }

    .table thead th {
      background-color: #3273dc !important;
      color: white !important;
      font-weight: 600 !important;
      border: none !important;
      border-color: #3273dc !important;
      padding: 1rem 0.75rem;
      text-align: center;
      vertical-align: middle;
      font-size: 0.9rem;
    }

    .table td {
      padding: 0.75rem;
      text-align: center;
      vertical-align: middle;
      border-bottom: 1px solid #dbdbdb;
      font-size: 0.9rem;
    }

    .table tbody tr:hover {
      background-color: #fafafa;
    }

    .action-buttons {
      display: flex;
      gap: 0.25rem;
      justify-content: center;
    }

    .action-buttons .button {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }

    .status-badge {
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.75rem;
      font-weight: 600;
    }

    .status-active {
      background-color: #48c774;
      color: white;
    }

    .status-sold {
      background-color: #f14668;
      color: white;
    }

    .status-holding {
      background-color: #ffdd57;
      color: #363636;
    }

    .amount-positive {
      color: #48c774;
      font-weight: 600;
    }

    .amount-negative {
      color: #f14668;
      font-weight: 600;
    }

    .amount-neutral {
      color: #363636;
    }
  </style>
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <!-- 頁面標題 -->
        <div class="mb-4">
          <h1 class="title is-4">
            <a href="/?main=資金管理"
              style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
            投資管理
          </h1>
        </div>
        <div class="main-content">
          <div class="header-section">
            <div class="header-buttons">
              <button class="button is-info is-light">頁面指南</button>
              <a href="/investment/create" class="button is-link">新增投資紀錄</a>
            </div>
          </div>

          <!-- 搜尋區域 -->
          <div class="search-section">
            <div class="field">
              <label class="label is-small">搜尋投資項目：</label>
              <div class="control">
                <input class="input" type="text" placeholder="輸入投資項目名稱" id="search-input">
              </div>
            </div>
            <div class="field">
              <label class="label is-small">投資類型：</label>
              <div class="control">
                <div class="select">
                  <select id="type-filter">
                    <option value="">全部類型</option>
                    <option value="股票">股票</option>
                    <option value="基金">基金</option>
                    <option value="債券">債券</option>
                    <option value="其他">其他</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="field">
              <button class="button is-primary" onclick="searchInvestments()">搜尋</button>
            </div>
          </div>

          <div class="table-container">
            <table class="table is-fullwidth">
              <thead>
                <tr>
                  <th>代碼</th>
                  <th>名稱</th>
                  <th>類型</th>
                  <th>購買日期</th>
                  <th>購買價格</th>
                  <th>購買數量</th>
                  <th>目前價格</th>
                  <th>目前市值</th>
                  <th>損益金額</th>
                  <th>損益率</th>
                  <th>狀態</th>
                  <th>動作</th>
                </tr>
              </thead>
              <tbody>
                {% if investments %}
                  {% for investment in investments %}
                  <tr>
                    <td>{{ investment.code or '-' }}</td>
                    <td>{{ investment.name or '-' }}</td>
                    <td>{{ investment.type or '-' }}</td>
                    <td>{{ investment.purchase_date or '-' }}</td>
                    <td>{{ investment.purchase_price or '0' }}</td>
                    <td>{{ investment.quantity or '0' }}</td>
                    <td>{{ investment.current_price or '0' }}</td>
                    <td>{{ investment.current_value or '0' }}</td>
                    <td>
                      {% if investment.profit_loss %}
                        {% if investment.profit_loss > 0 %}
                          <span class="amount-positive">+{{ investment.profit_loss }}</span>
                        {% elif investment.profit_loss < 0 %}
                          <span class="amount-negative">{{ investment.profit_loss }}</span>
                        {% else %}
                          <span class="amount-neutral">{{ investment.profit_loss }}</span>
                        {% endif %}
                      {% else %}
                        -
                      {% endif %}
                    </td>
                    <td>
                      {% if investment.profit_loss_rate %}
                        {% if investment.profit_loss_rate > 0 %}
                          <span class="amount-positive">+{{ investment.profit_loss_rate }}%</span>
                        {% elif investment.profit_loss_rate < 0 %}
                          <span class="amount-negative">{{ investment.profit_loss_rate }}%</span>
                        {% else %}
                          <span class="amount-neutral">{{ investment.profit_loss_rate }}%</span>
                        {% endif %}
                      {% else %}
                        -
                      {% endif %}
                    </td>
                    <td>
                      {% if investment.status == '持有中' %}
                        <span class="status-badge status-holding">持有中</span>
                      {% elif investment.status == '已賣出' %}
                        <span class="status-badge status-sold">已賣出</span>
                      {% else %}
                        <span class="status-badge status-active">{{ investment.status or '持有中' }}</span>
                      {% endif %}
                    </td>
                    <td>
                      <div class="action-buttons">
                        <a href="/investment/edit/{{ investment.id }}" class="button is-small is-warning is-light">編輯</a>
                        <button class="button is-small is-danger is-light" onclick="deleteInvestment({{ investment.id }})">刪除</button>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td colspan="12" class="has-text-grey">目前沒有投資記錄</td>
                  </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
  <script>
    function searchInvestments() {
      const searchTerm = document.getElementById('search-input').value;
      const typeFilter = document.getElementById('type-filter').value;
      
      // 這裡可以實作搜尋邏輯
      console.log('搜尋條件:', { searchTerm, typeFilter });
      
      // 暫時重新載入頁面，實際應該用 AJAX 查詢
      // location.reload();
    }

    function deleteInvestment(id) {
      if (confirm('確定要刪除這筆投資記錄嗎？')) {
        fetch(`/investment/delete/${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          }
        })
        .then(response => {
          if (response.ok) {
            location.reload();
          } else {
            alert('刪除失敗，請稍後再試');
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('刪除失敗，請稍後再試');
        });
      }
    }

    // 搜尋框 Enter 鍵支援
    document.getElementById('search-input').addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        searchInvestments();
      }
    });
  </script>
</body>

</html>
