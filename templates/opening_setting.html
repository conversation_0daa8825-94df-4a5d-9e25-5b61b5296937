<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>開帳設定</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        body {
            background: #f7f8fa;
            margin: 0;
            padding: 0;
        }

        .column.is-narrow {
            flex: none;
            width: 200px !important;
            max-width: 200px !important;
            min-width: 200px !important;
        }

        .sidebar {
            max-width: 200px;
            min-width: 180px;
            width: 200px;
        }

        .sidebar .menu-label {
            font-size: 14px;
            font-weight: bold;
        }

        .sidebar .menu-list a {
            font-size: 20px;
            padding: 0.5em 0.75em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .container-box {
            max-width: 1400px;
            margin: 40px auto;
        }

        .main-title {
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 2px;
        }

        .panel-title {
            background: #234a93;
            color: #fff;
            padding: 0.8rem 1.5rem;
            border-radius: 12px 12px 0 0;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .box-shadow {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            border-radius: 12px;
            border: none;
        }

        .mb-0 {
            margin-bottom: 0 !important;
        }

        .mb-1 {
            margin-bottom: 0.5rem !important;
        }

        .mb-2 {
            margin-bottom: 1rem !important;
        }

        .mb-3 {
            margin-bottom: 1.5rem !important;
        }

        .mt-2 {
            margin-top: 1rem !important;
        }

        .mt-4 {
            margin-top: 2rem !important;
        }

        .ml-2 {
            margin-left: 0.75rem !important;
        }

        .ml-4 {
            margin-left: 1.5rem !important;
        }

        .is-pulled-right {
            float: right;
        }

        .flex-between {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .subtitle-btn {
            background: #f5f7fa;
            border: none;
            color: #234a93;
            font-weight: bold;
            border-radius: 8px;
            padding: 0.4rem 1.2rem;
            margin-left: 1rem;
        }

        .form-label {
            font-weight: bold;
            margin-bottom: 0.3rem;
            color: #222;
        }

        .debit-header {
            background: #234a93;
            color: #fff;
            font-size: 1.2rem;
            font-weight: bold;
            border-radius: 8px 8px 0 0;
            padding: 0.7rem 1.2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .credit-header {
            background: #ffe066;
            color: #222;
            font-size: 1.2rem;
            font-weight: bold;
            border-radius: 8px 8px 0 0;
            padding: 0.7rem 1.2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .debit-table,
        .credit-table {
            width: 100%;
            background: transparent;
            margin-bottom: 0.5rem;
        }

        .debit-table th,
        .debit-table td {
            background: #eaf1fb;
        }

        .credit-table th,
        .credit-table td {
            background: #fffbe6;
        }

        .debit-table th,
        .credit-table th {
            font-weight: bold;
            color: #222;
        }

        .debit-table td,
        .credit-table td {
            padding: 0.4rem 0.5rem;
        }

        .debit-footer,
        .credit-footer {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-top: 0.5rem;
        }

        .debit-footer-btn {
            background: #234a93;
            color: #234a93;
            border: 1px solid #234a93;
            color: #234a93;
            background: #fff;
        }

        .debit-footer-btn:hover {
            background: #234a93;
            color: #fff;
        }

        .credit-footer-btn {
            background: #ffe066;
            color: #e6b800;
            border: 1px solid #e6b800;
            background: #fff;
        }

        .credit-footer-btn:hover {
            background: #e6b800;
            color: #fff;
        }

        .sum-label {
            font-weight: bold;
        }

        .right-box-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #234a93;
            color: #fff;
            border-radius: 12px 12px 0 0;
            padding: 0.8rem 1.5rem;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .right-box-header .right {
            background: #234a93;
            color: #fff;
            border-radius: 8px;
            font-size: 1rem;
            padding: 0.2rem 1.2rem;
            margin-left: 1rem;
        }

        .right-box {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            padding: 0;
        }

        .right-box-content {
            padding: 1.5rem 1.5rem 1.2rem 1.5rem;
        }

        @media (max-width: 1024px) {
            .container-box {
                max-width: 98vw;
                padding: 0 0.5rem;
            }
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=設定"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        開帳設定
                    </h1>
                </div>
                <div class="mb-4">
                    <div style="float:right;">
                        <form method="post" action="/opening_setting/complete">
                            <button class="button is-primary is-medium" type="submit">
                                <span class="icon"><i class="fas fa-check"></i></span>
                                <span>完成開帳</span>
                            </button>
                        </form>
                    </div>
                </div>
                <div class="columns is-variable is-2">
                    <!-- 左側：開帳基本資料 -->
                    <div class="column is-4">
                        <div class="box box-shadow">
                            <div class="panel-title">開帳基本資料</div>
                            <div class="p-4">
                                <div class="field mb-4">
                                    <label class="form-label">開帳日期</label>
                                    <div class="control">
                                        <input class="input" type="date" value="2025-06-29">
                                    </div>
                                </div>
                                {% for acc in accounts %}
                                <div class="mb-2">
                                    <div>{{ acc.full_code }}-{{ acc.name }}</div>
                                    <input class="input" type="text" value="{{ acc.init_amount or 0 }}" readonly
                                        style="text-align: right;">
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    <!-- 右側：第二步分錄 -->
                    <div class="column is-8">
                        <div class="right-box">
                            <div class="right-box-header">
                                <span>第二步-確認開帳會計分錄(可忽略)</span>
                                <span class="right">開帳差異數：<b id="diff-sum">0</b></span>
                            </div>
                            <div class="right-box-content">
                                <!-- 借方區塊 -->
                                <div class="mb-4">
                                    {% set ns = namespace(debit_sum=0) %}
                                    {% for acc in accounts %}
                                    {% set ns.debit_sum = ns.debit_sum + (acc.init_amount or 0) %}
                                    {% endfor %}
                                    <div class="debit-header">
                                        <span>借方</span>
                                        <span style="font-size:1rem;font-weight:normal;">借方合計：<b>{{ ns.debit_sum
                                                }}</b></span>
                                    </div>
                                    <table class="table debit-table is-fullwidth mb-1">
                                        <thead>
                                            <tr>
                                                <th>科目</th>
                                                <th>借方金額</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for acc in accounts %}
                                            <tr>
                                                <td><input class="input" type="text"
                                                        value="{{ acc.full_code }}-{{ acc.name }}" readonly></td>
                                                <td><input class="input" type="text" value="{{ acc.init_amount or 0 }}"
                                                        readonly style="text-align: right;"></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                    <div class="debit-footer">
                                        <button class="button is-outlined debit-footer-btn">新增借方項目</button>
                                    </div>
                                </div>
                                <!-- 貸方區塊 -->
                                <div>
                                    <div class="credit-header">
                                        <span>貸方</span>
                                        <span style="font-size:1rem;font-weight:normal;">貸方合計：<b
                                                id="credit-sum">0</b></span>
                                    </div>
                                    <table class="table credit-table is-fullwidth mb-1">
                                        <thead>
                                            <tr>
                                                <th>科目</th>
                                                <th>貸方金額</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><input class="input" type="text" value="3110-資本" readonly></td>
                                                <td><input class="input credit-amount" type="number" placeholder="$"
                                                        value="0"></td>
                                            </tr>
                                            <tr>
                                                <td><input class="input" type="text" value="3420-資本公積" readonly></td>
                                                <td><input class="input credit-amount" type="number" placeholder="$"
                                                        value="0"></td>
                                            </tr>
                                            <tr>
                                                <td><input class="input" type="text" value="3533-累積盈虧" readonly></td>
                                                <td><input class="input credit-amount" type="number" placeholder="$"
                                                        value="0"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <div class="credit-footer">
                                        <button class="button is-outlined credit-footer-btn">新增貸方項目</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script>
        function updateCreditSum() {
            let sum = 0;
            document.querySelectorAll('.credit-amount').forEach(function (input) {
                let val = parseInt(input.value.replace(/,/g, ''), 10);
                if (!isNaN(val)) sum += val;
            });
            document.getElementById('credit-sum').textContent = sum.toLocaleString();

            // 取得借方合計
            let debitSum = 0;
            let debitSumElem = document.querySelector('.debit-header b');
            if (debitSumElem) {
                debitSum = parseInt(debitSumElem.textContent.replace(/,/g, ''), 10) || 0;
            }
            // 計算差異數
            let diff = Math.abs(debitSum - sum);
            document.getElementById('diff-sum').textContent = diff.toLocaleString();
        }

        // 綁定事件
        window.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('.credit-amount').forEach(function (input) {
                input.addEventListener('blur', updateCreditSum);
                input.addEventListener('input', updateCreditSum);
            });
            updateCreditSum();
        });
    </script>
</body>

</html>