<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成本結轉</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f5f6fa;
        }

        .column.is-narrow {
            flex: none;
            width: 200px !important;
            max-width: 200px !important;
            min-width: 200px !important;
        }

        .sidebar {
            max-width: 200px;
            min-width: 180px;
            width: 200px;
        }

        .sidebar .menu-label {
            font-size: 14px;
            font-weight: bold;
        }

        .sidebar .menu-list a {
            font-size: 20px;
            padding: 0.5em 0.75em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .main-content {
            padding: 2rem 1rem;
        }

        .header-section {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .back-link {
            font-size: 1.5rem;
            color: #363636;
            text-decoration: none;
            margin-right: 1rem;
            font-weight: 600;
        }

        .back-link:hover {
            color: #3273dc;
        }

        .header-buttons {
            margin-left: auto;
            display: flex;
            gap: 0.5rem;
        }

        .year-selector {
            background: white;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .table-container {
            background: white;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
        }

        .table {
            margin-bottom: 0;
        }

        .table th {
            background-color: #3273dc;
            color: white;
            font-weight: 600;
            border: none;
            padding: 1rem 0.75rem;
            text-align: center;
            vertical-align: middle;
            font-size: 0.9rem;
        }

        .table td {
            padding: 0.75rem;
            text-align: center;
            vertical-align: middle;
            border-bottom: 1px solid #dbdbdb;
            font-size: 0.9rem;
        }

        .table tbody tr:hover {
            background-color: #fafafa;
        }

        .cost-btn {
            background: #48c774;
            color: #fff;
            border-radius: 4px;
            font-weight: 600;
            border: none;
            padding: 0.375rem 0.75rem;
            cursor: pointer;
            font-size: 0.75rem;
            text-decoration: none;
            display: inline-block;
        }

        .cost-btn:hover {
            background: #3ec46d;
            color: #fff;
        }

        .month-cell {
            font-weight: 600;
            background-color: #f8f9fa !important;
        }

        .amount-cell {
            font-family: 'Courier New', monospace;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="main-content">
                    <div class="header-section">
                        <a href="/?main=收支帳簿" class="back-link">← 成本結轉</a>
                        <div class="header-buttons">
                            <button class="button is-info is-light">頁面指南</button>
                        </div>
                    </div>

                    <div class="year-selector">
                        <span class="has-text-weight-semibold">查詢年份：</span>
                        <div class="select">
                            <select>
                                <option value="2025">2025年</option>
                                <option value="2024">2024年</option>
                                <option value="2023">2023年</option>
                            </select>
                        </div>
                        <button class="button is-primary is-small">查詢</button>
                    </div>

                    <div class="table-container">
                        <table class="table is-fullwidth">
                            <thead>
                                <tr>
                                    <th style="width: 80px;">月份</th>
                                    <th>期初存貨</th>
                                    <th>本期進貨</th>
                                    <th>銷貨成本</th>
                                    <th>期末存貨</th>
                                    <th style="width: 100px;">動作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for m in range(1, 13) %}
                                <tr>
                                    <td class="month-cell">{{ m }}月</td>
                                    <td class="amount-cell">$0</td>
                                    <td class="amount-cell">$0</td>
                                    <td class="amount-cell">$0</td>
                                    <td class="amount-cell">$0</td>
                                    <td>
                                        <a class="cost-btn" href="/cost_transfer_detail/{{ m }}">結轉</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="notification is-info is-light mt-4">
                        <p><strong>說明：</strong></p>
                        <ul>
                            <li>成本結轉用於計算每月的銷貨成本</li>
                            <li>公式：期初存貨 + 本期進貨 - 期末存貨 = 銷貨成本</li>
                            <li>點擊「結轉」按鈕可進行該月份的成本結轉作業</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>