<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>收支對象管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=設定"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        收支對象管理
                    </h1>
                </div>
                <div class="box">
                    <div class="level">
                        <div class="level-left">
                        </div>
                        <div class="level-right">
                            <a href="/payment_identity/add" class="button is-warning is-rounded">
                                <span class="icon"><i class="fas fa-plus"></i></span>
                                <span>新增收支對象</span>
                            </a>
                        </div>
                    </div>
                    <div class="tabs is-toggle is-toggle-rounded mb-4">
                        <ul>
                            <li class="{% if type == '客戶' %}is-active{% endif %}"><a
                                    href="{{ url_for('settings.payment_identity_list', type='客戶') }}">客戶</a></li>
                            <li class="{% if type == '供應商' %}is-active{% endif %}"><a
                                    href="{{ url_for('settings.payment_identity_list', type='供應商') }}">供應商</a></li>
                            <li class="{% if type == '其他' %}is-active{% endif %}"><a
                                    href="{{ url_for('settings.payment_identity_list', type='其他') }}">其他</a></li>
                        </ul>
                    </div>
                    <table class="table is-fullwidth is-striped is-hoverable">
                        <thead>
                            <tr>
                                <th>客戶類型</th>
                                <th>公司名稱</th>
                                <th>公司統編</th>
                                <th>銀行代碼</th>
                                <th>銀行帳號</th>
                                <th>聯絡人姓名</th>
                                <th>手機</th>
                                <th>Line ID</th>
                                <th>備註</th>
                                <th>編輯</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for i in identities %}
                            <tr>
                                <td>{{ i.type }}</td>
                                <td>{{ i.name }}</td>
                                <td>{{ i.tax_id }}</td>
                                <td>{{ i.bank_code }}</td>
                                <td>{{ i.bank_account }}</td>
                                <td>{{ i.contact }}</td>
                                <td>{{ i.mobile }}</td>
                                <td>{{ i.line }}</td>
                                <td>{{ i.note }}</td>
                                <td>
                                    <a href="/payment_identity/edit/{{ i.id }}" class="icon has-text-link" title="編輯">
                                        <i class="fas fa-pen"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>

</html>