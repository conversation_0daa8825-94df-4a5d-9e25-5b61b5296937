<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>印錢大師</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        {% if transfer_form %}
        {% include 'transfer_form.html' %}
        {% elif selected_buttons %}
        <div class="box mb-5">
          <h2 class="subtitle">
            <a href="/?main={{ selected }}"
              style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
            {{ button_label }}
          </h2>
          <div class="buttons">
            {% for child in selected_buttons %}
            {% if child == '收入紀錄' %}
            <a class="button is-info is-light" href="/income_record">{{ child }}</a>
            {% elif child == '支出紀錄' %}
            <a class="button is-info is-light" href="/expense_record">{{ child }}</a>
            {% else %}
            <button class="button is-info is-light">{{ child }}</button>
            {% endif %}
            {% endfor %}
          </div>
        </div>
        {% else %}
        <!-- 主選單頁面標題 -->
        <div class="mb-4">
          <h1 class="title is-4">
            <a href="/"
              style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
            {{ selected }}
          </h1>
        </div>
        {% for submenu in submenus %}
        <div class="box mb-5">
          <h2 class="subtitle">{{ submenu.title }}</h2>
          <div class="buttons">
            {% for btn in submenu.buttons %}
            {% if btn.label == '新增移轉紀錄' %}
            <a class="button is-link is-light" href="/transfer">{{ btn.label }}</a>
            {% elif btn.label == '資金移轉列表' %}
            <a class="button is-link is-light" href="/transfer/list">{{ btn.label }}</a>

            {# 不顯示這個按鈕 #}
            {% elif btn.label == '分享帳簿' %}
            <a class="button is-link is-light" href="/share_account/list">{{ btn.label }}</a>
            {% elif btn.label == '新增預付費用' %}
            <a class="button is-link is-light" href="/add_prepaid_expense">{{ btn.label }}</a>
            {% elif btn.label == '新增各項攤提' %}
            <a class="button is-link is-light" href="/add_amortization">{{ btn.label }}</a>
            {% elif btn.label == '新增固定資產' %}
            <a class="button is-link is-light" href="/add_fixed_asset">{{ btn.label }}</a>
            {% elif btn.label == '新增 無形資產/商譽' %}
            <a class="button is-link is-light" href="/add_intangible_asset">{{ btn.label }}</a>
            {% elif btn.label == '財產列表' %}
            <a class="button is-link is-light" href="/asset_list">{{ btn.label }}</a>
            {% elif btn.label == '發薪作業' %}
            <a class="button is-link is-light" href="/payroll_process">{{ btn.label }}</a>
            {% elif btn.label == '勞保/健保/退休金管理' %}
            <a class="button is-link is-light" href="/insurance_manage">{{ btn.label }}</a>
            {% elif btn.label == '新增員工' %}
            <a class="button is-link is-light" href="/add_employee">{{ btn.label }}</a>
            {% elif btn.label == '員工管理列表' %}
            <a class="button is-link is-light" href="/employee_list">{{ btn.label }}</a>
            {% elif btn.label == '公司設定' %}
            <a class="button is-link is-light" href="/company_setting">{{ btn.label }}</a>
            {% elif btn.label == '薪資設定' %}
            <a class="button is-link is-light" href="/salary_setting">{{ btn.label }}</a>
            {% elif btn.label == '建立勞報單' or btn.label == '新增勞務報酬單' %}
            <a class="button is-link is-light" href="/add_service_reward">{{ btn.label }}</a>
            {% elif btn.label == '勞務報酬列表' %}
            <a class="button is-link is-light" href="/service_reward_list">{{ btn.label }}</a>
            {% elif btn.label == '扣繳申報作業' %}
            <a class="button is-link is-light" href="/withholding_declare">{{ btn.label }}</a>
            {% elif btn.label == '部門管理' %}
            <a class="button is-link is-light" href="/department_manage">{{ btn.label }}</a>
            {% elif btn.label == '專案管理' %}
            <a class="button is-link is-light" href="/project_manage">{{ btn.label }}</a>
            {% elif btn.label == '收支對象管理' %}
            <a class="button is-link is-light" href="/payment_identity_list">{{ btn.label }}</a>
            {% elif btn.label == '帳戶設定' %}
            <a class="button is-link is-light" href="/account_setting">{{ btn.label }}</a>
            {% elif btn.label == '開帳設定' %}
            <a class="button is-link is-light" href="/opening_setting">{{ btn.label }}</a>
            {% elif btn.label == '新增銀行借款' %}
            <a class="button is-link is-light" href="/bankloan/create">{{ btn.label }}</a>
            {% elif btn.label == '銀行借款列表' %}
            <a class="button is-link is-light" href="/bankloan/list">{{ btn.label }}</a>
            {% elif btn.children and btn.children|length > 0 %}
            <a class="button is-link is-light"
              href="/?main={{ selected }}&submenu={{ submenu.title }}&button={{ btn.label }}">{{ btn.label
              }}</a>
            {% elif btn.label == '收入紀錄列表' %}
            <a class="button is-link is-light" href="/income_list">{{ btn.label }}</a>
            {% elif btn.label == '支出紀錄列表' %}
            <a class="button is-link is-light" href="/expense_list">{{ btn.label }}</a>
            {% elif btn.label == '應收應付逾期' %}
            <a class="button is-link is-light" href="/ac_delay_list">{{ btn.label }}</a>
            {% elif btn.label == '傳票管理' %}
            <a class="button is-link is-light" href="/voucher_manage">{{ btn.label }}</a>
            {% elif btn.label == '科目管理' %}
            <a class="button is-link is-light" href="/accounting/subject_manage">{{ btn.label }}</a>
            {% elif btn.label == '成本結轉' %}
            <a class="button is-link is-light" href="/cost_transfer">{{ btn.label }}</a>
            {% elif btn.label == '進/銷項稅額管理' %}
            <a class="button is-link is-light" href="/tax_manage">{{ btn.label }}</a>
            {% elif btn.label == '基本資料' %}
            <a class="button is-link is-light" href="/basic_info">{{ btn.label }}</a>
            {% elif btn.label == '新增資金紀錄' %}
            <a class="button is-link is-light" href="/fund_record/create">{{ btn.label }}</a>
            {% elif btn.label == '資金紀錄列表' %}
            <a class="button is-link is-light" href="/fund_record/list">{{ btn.label }}</a>
            {% elif btn.label == '投資管理' %}
            <a class="button is-link is-light" href="/investment_manage">{{ btn.label }}</a>
            {% elif btn.label == '帳戶明細' %}
            <a class="button is-link is-light" href="/account_detail">{{ btn.label }}</a>
            {% else %}
            <button class="button is-link is-light">{{ btn.label }}</button>
            {% endif %}
            {% endfor %}
          </div>
        </div>
        {% endfor %}
        {% endif %}
      </div>
    </div>
  </div>
  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</body>

</html>