<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>新增員工</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        .main-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .form-section {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px #eee;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .field {
            margin-bottom: 1.2rem;
        }

        .is-required:after {
            content: '*';
            color: #e53e3e;
            margin-left: 0.2em;
        }

        .form-note {
            color: #888;
            font-size: 0.95em;
            margin-bottom: 1em;
        }

        .blue-bar {
            background: #f5f6fa;
            border-left: 4px solid #2563eb;
            padding: 0.7em 1.2em;
            margin: 2em 0 1em 0;
            color: #2563eb;
            font-weight: bold;
        }

        .select select,
        .input[type='date'] {
            height: 2.5em;
        }

        .label {
            min-width: 7em;
        }
    </style>
</head>

<body style="background:#f5f6fa;">
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="main-title mt-5 mb-4">
                    <a href="javascript:history.back()"
                        style="color:#222;text-decoration:none;font-size:1.5rem;vertical-align:middle;">←</a>
                    {% if is_edit %}編輯員工{% else %}新增員工{% endif %}
                </div>
                <form method="post" class="form-section">
                    <div class="section-title">基本資料</div>
                    <div class="columns is-multiline">
                        <div class="column is-2">
                            <div class="field"><label class="label">職稱</label><input class="input" type="text"
                                    name="title" value="{{ employee.title if employee else '' }}" placeholder="請輸入職稱">
                            </div>
                        </div>
                        <div class="column is-2">
                            <div class="field"><label class="label">員工編號</label><input class="input" type="text"
                                    name="emp_id" placeholder="請輸入員工編號"></div>
                        </div>
                        <div class="column is-3">
                            <div class="field"><label class="label">到職日</label><input class="input" type="date"
                                    name="onboard_date"></div>
                        </div>
                        <div class="column is-2">
                            <div class="field"><label class="label">部門</label><input class="input" type="text"
                                    name="department" placeholder="請輸入部門"></div>
                        </div>
                        <div class="column is-3">
                            <div class="field"><label class="label">身份證號</label><input class="input" type="text"
                                    name="identity" placeholder="請輸入身份證號"></div>
                        </div>
                    </div>
                    <div class="columns is-multiline">
                        <div class="column is-3">
                            <div class="field"><label class="label">姓名</label><input class="input" type="text"
                                    name="name" placeholder="請輸入姓名"></div>
                        </div>
                        <div class="column is-3">
                            <div class="field"><label class="label">通訊地址</label><input class="input" type="text"
                                    name="address" placeholder="請輸入通訊地址"></div>
                        </div>
                        <div class="column is-2">
                            <div class="field"><label class="label">聯絡電話</label><input class="input" type="text"
                                    name="phone" placeholder="請輸入聯絡電話"></div>
                        </div>
                        <div class="column is-2">
                            <div class="field"><label class="label">電子信箱</label><input class="input" type="email"
                                    name="email" placeholder="請輸入電子信箱"></div>
                        </div>
                        <div class="column is-2">
                            <div class="field"><label class="label">離職日</label><input class="input" type="date"
                                    name="leave_date"></div>
                        </div>
                    </div>
                    <div class="section-title">薪資資訊</div>
                    <div class="columns is-multiline">
                        <div class="column is-3">
                            <div class="field"><label class="label">本薪</label><input class="input" type="number"
                                    name="salary" value="0"></div>
                        </div>
                        <div class="column is-3">
                            <div class="field"><label class="label">伙食費</label><input class="input" type="number"
                                    name="meal" value="0"></div>
                        </div>
                        <div class="column is-3">
                            <div class="field"><label class="label">薪資匯款銀行</label><input class="input" type="text"
                                    name="bank"></div>
                        </div>
                        <div class="column is-3">
                            <div class="field"><label class="label">薪資匯款帳號</label><input class="input" type="text"
                                    name="bank_account"></div>
                        </div>
                    </div>
                    <!-- 新增保險區塊 -->
                    <div class="section-title">保險</div>
                    <div class="columns is-vcentered">
                        <div class="column is-4">
                            <label class="label">保險身份 <span class="is-required"></span></label>
                            <div class="select is-fullwidth">
                                <select name="insurance_identity" required>
                                    <option value="">請選擇保險身份</option>
                                    <option value="負責人">負責人</option>
                                    <option value="勞工">勞工</option>
                                </select>
                            </div>
                        </div>
                        <div class="column is-4">
                            <label class="label">是否投保勞保、勞退 <span class="is-required"></span></label>
                            <div class="field is-grouped">
                                <div class="control">
                                    <label class="radio">
                                        <input type="radio" name="labor_insurance" value="yes" checked>
                                        是
                                    </label>
                                </div>
                                <div class="control">
                                    <label class="radio">
                                        <input type="radio" name="labor_insurance" value="no">
                                        否
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="column is-4">
                            <label class="label">是否投保健保 <span class="is-required"></span></label>
                            <div class="field is-grouped">
                                <div class="control">
                                    <label class="radio">
                                        <input type="radio" name="health_insurance" value="yes" checked>
                                        是
                                    </label>
                                </div>
                                <div class="control">
                                    <label class="radio">
                                        <input type="radio" name="health_insurance" value="no">
                                        否
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 動態顯示區塊 start -->
                    <div id="health-section">
                        <div class="section-title">健康保險</div>
                        <div class="columns">
                            <div class="column is-12">
                                <div class="columns">
                                    <div class="column is-2">
                                        <div class="field">
                                            <label class="label">
                                                加保日
                                                <span class="icon has-text-info" title="加保日說明"
                                                    style="vertical-align: middle;">
                                                    <i class="fas fa-question-circle"></i>
                                                </span>
                                            </label>
                                            <div class="control">
                                                <input class="input" type="date" name="health_insurance_date">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-4">
                                        <div class="field">
                                            <label class="label">本人健保補助資格 <span style="color:#e53e3e;">*</span></label>
                                            <div class="control">
                                                <div class="select is-fullwidth">
                                                    <select name="health_subsidy_qualification" required>
                                                        <option value="">請選擇補助資格</option>
                                                        <option value="無">無</option>
                                                        <option value="補助1/4">補助1/4</option>
                                                        <option value="補助1/2">補助1/2</option>
                                                        <option value="補助全額">補助全額</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-3">
                                        <div class="field">
                                            <label class="label">健保級距法規生效日 <span style="color:#e53e3e;">*</span></label>
                                            <div class="control has-icons-right">
                                                <input class="input" type="date" name="health_law_effective_date"
                                                    required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-3">
                                        <div class="field">
                                            <label class="label">健保投保級距 <span style="color:#e53e3e;">*</span></label>
                                            <div class="control">
                                                <div class="select is-fullwidth">
                                                    <select name="health_level" required>
                                                        <option value="">請選擇投保級距</option>
                                                        <option value="1">1級</option>
                                                        <option value="2">2級</option>
                                                        <option value="3">3級</option>
                                                        <!-- 依實際級距選項補齊 -->
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="columns is-multiline">
                                    <div class="column is-12">
                                        <div class="field">
                                            <label class="label">健保眷屬 <span
                                                    style="font-weight:normal; color:#888; font-size:0.95em;">(依附加保眷屬超過3人時，連同被保險人本人，保險費最多計收4人)</span></label>
                                            <div class="columns is-multiline">
                                                <div class="column is-2">
                                                    <div class="field"><label class="label"
                                                            style="font-weight:normal;">無補助</label><input class="input"
                                                            type="number" name="dependents_none"
                                                            value="0"><span>人</span></div>
                                                </div>
                                                <div class="column is-2">
                                                    <div class="field"><label class="label"
                                                            style="font-weight:normal;">補助1/4</label><input
                                                            class="input" type="number" name="dependents_1_4"
                                                            value="0"><span>人</span></div>
                                                </div>
                                                <div class="column is-2">
                                                    <div class="field"><label class="label"
                                                            style="font-weight:normal;">補助1/2</label><input
                                                            class="input" type="number" name="dependents_1_2"
                                                            value="0"><span>人</span></div>
                                                </div>
                                                <div class="column is-2">
                                                    <div class="field"><label class="label"
                                                            style="font-weight:normal;">補助地區人口保費</label><input
                                                            class="input" type="number" name="dependents_local"
                                                            value="0"><span>人</span></div>
                                                </div>
                                                <div class="column is-2">
                                                    <div class="field"><label class="label"
                                                            style="font-weight:normal;">補助全額</label><input class="input"
                                                            type="number" name="dependents_full"
                                                            value="0"><span>人</span></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> <!-- 健康保險 end -->
                    <div id="labor-section">
                        <div class="section-title">勞工保險</div>
                        <!-- 勞保投保項目移到上面 -->
                        <div class="columns">
                            <div class="column is-12">
                                <div class="field">
                                    <label class="label">勞保投保項目</label>
                                    <div class="control">
                                        <label class="checkbox mr-3">
                                            <input type="checkbox" name="labor_insurance_item" value="普通事故保險"> 普通事故保險
                                        </label>
                                        <label class="checkbox mr-3" style="position:relative;">
                                            <input type="checkbox" id="occupational_ins_checkbox"
                                                name="labor_insurance_item" value="職業災害保險"> 職業災害保險
                                        </label>
                                        <label class="checkbox">
                                            <input type="checkbox" name="labor_insurance_item" value="就業保險"> 就業保險
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="columns">
                            <div class="column is-12">
                                <strong>由公司為勞工進行加保，是一種強制雇主應為勞工加保的制度。其收費的比例為：勞工自費20%、雇主70%、政府10%。</strong>
                            </div>
                        </div>
                        <div class="columns is-vcentered">
                            <div class="column is-2">
                                <div class="field">
                                    <label class="label"
                                        style="height: 2.5em; display: flex; align-items: flex-end;">加保日 <span
                                            class="icon has-text-info" title="加保日說明"><i
                                                class="fas fa-question-circle"></i></span></label>
                                    <div class="control">
                                        <input class="input" type="date" name="labor_insurance_date">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label"
                                        style="height: 2.5em; display: flex; align-items: flex-end;">勞保級距法規生效日 <span
                                            style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <input class="input" type="date" name="labor_law_effective_date" required>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="label"
                                        style="height: 2.5em; display: flex; align-items: flex-end;">勞保投保級距 <span
                                            style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="labor_level" required>
                                                <option value="">請選擇投保級距</option>
                                                <option value="1">1級</option>
                                                <option value="2">2級</option>
                                                <option value="3">3級</option>
                                                <!-- 依實際級距選項補齊 -->
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 職業災害保險動態欄位 -->
                            <div class="column is-3" id="occupational-date-field" style="display:none;">
                                <div class="field">
                                    <label class="label"
                                        style="height: 2.5em; display: flex; align-items: flex-end;">職保級距法規生效日 <span
                                            style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <input class="input" type="date" name="occupational_law_effective_date">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2" id="occupational-level-field" style="display:none;">
                                <div class="field">
                                    <label class="label"
                                        style="height: 2.5em; display: flex; align-items: flex-end;">職保投保級距 <span
                                            style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="occupational_level">
                                                <option value="">請選擇投保級距</option>
                                                <option value="1">1級</option>
                                                <option value="2">2級</option>
                                                <option value="3">3級</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div> <!-- 勞工保險 end -->
                    <div class="field is-grouped is-grouped-right mt-5">
                        <div class="control">
                            <button class="button is-link" type="submit">儲存</button>
                        </div>
                        <div class="control">
                            <a class="button is-light" href="/">取消</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const occCheckbox = document.getElementById('occupational_ins_checkbox');
        const occDateField = document.getElementById('occupational-date-field');
        const occLevelField = document.getElementById('occupational-level-field');

        if (occCheckbox && occDateField && occLevelField) {
            function toggleOccFields() {
                const display = occCheckbox.checked ? '' : 'none';
                occDateField.style.display = display;
                occLevelField.style.display = display;
            }
            occCheckbox.addEventListener('change', toggleOccFields);
            toggleOccFields(); // 頁面載入時自動判斷
        }
    });
</script>

</html>