<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支出紀錄 - 使用模組化範例</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .tab-custom {
            background: #dc2626;
            color: #fff;
            border-radius: 8px 8px 0 0;
            padding: 0.75rem 2rem;
            font-weight: bold;
            font-size: 1.2rem;
        }
        .box-shadow {
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
    </style>
</head>
<body>
    <div class="container is-fluid">
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="box box-shadow" style="padding:0;">
                    <div class="tab-custom">支出紀錄</div>
                    <form class="p-5" method="post" enctype="multipart/form-data">
                        <div class="columns">
                            <div class="column is-6">
                                <!-- 基本資料欄位 -->
                                <div class="field">
                                    <label class="label">記帳時間</label>
                                    <div class="control has-icons-right">
                                        <input class="input" type="date" name="a_time" value="2025-07-01">
                                        <span class="icon is-small is-right">
                                            <i class="fas fa-calendar"></i>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="field">
                                    <label class="label">支出名稱</label>
                                    <div class="control">
                                        <input class="input" type="text" name="name" placeholder="請輸入支出名稱">
                                    </div>
                                </div>
                                
                                <div class="field">
                                    <label class="label">金額</label>
                                    <div class="control">
                                        <input class="input" type="number" name="total" placeholder="請輸入金額">
                                    </div>
                                </div>
                                
                                <!-- 使用更多功能模組 -->
                                {% include 'more_section.html' %}
                            </div>
                            
                            <div class="column is-6">
                                <!-- 使用憑證模組 -->
                                {% include 'voucher_section.html' %}
                                
                                <!-- 使用收付款狀態模組 -->
                                {% include 'payment_status_section.html' %}
                            </div>
                        </div>
                        
                        <div class="field is-grouped is-justify-content-flex-end mt-5">
                            <div class="control">
                                <button class="button is-danger is-large" type="submit">儲存支出</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html>