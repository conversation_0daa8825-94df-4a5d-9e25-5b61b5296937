<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增銀行帳戶</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css" />
    <style>
        .container-box { max-width: 1400px; margin: 40px auto; }
        .main-card { background: #fff; border-radius: 18px; box-shadow: 0 2px 8px #eee; padding: 2.5rem 2rem; max-width: 600px; margin: 2rem auto; }
        .section-title { font-size: 1.3rem; font-weight: bold; color: #2563eb; margin-bottom: 1.2rem; }
        .back-btn { margin-bottom: 1rem; }
    </style>
</head>
<body>
<div class="container is-fluid container-box">
    <div class="columns">
        <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
            <div class="main-card">
                <div class="back-btn">
                    <a href="/account/bank" class="button is-light is-small">
                        <span class="icon"><i class="fas fa-arrow-left"></i></span>
                        <span>返回銀行帳戶</span>
                    </a>
                </div>
                
                <div class="section-title mb-5">
                    新增銀行帳戶
                </div>
                
                <form method="POST">
                    <div class="field">
                        <label class="label">帳戶名稱 *</label>
                        <div class="control">
                            <input class="input" type="text" name="name" placeholder="請輸入銀行帳戶名稱" required>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">銀行名稱 *</label>
                        <div class="control">
                            <div class="select is-fullwidth">
                                <select id="bank-head-select" name="bank_name" required>
                                    <option value="">請選擇銀行</option>
                                </select>
                            </div>
                        </div>
                        <div class="control mt-2" id="otherBankField" style="display: none;">
                            <input class="input" type="text" name="other_bank_name" placeholder="請輸入銀行名稱">
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">分行</label>
                        <div class="control">
                            <div class="select is-fullwidth">
                                <select id="bank-branch-select" name="branch">
                                    <option value="">請先選擇銀行</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">帳號 *</label>
                        <div class="control">
                            <input class="input" type="text" name="account_number" placeholder="請輸入帳號" required>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">戶名 *</label>
                        <div class="control">
                            <input class="input" type="text" name="account_holder" placeholder="請輸入戶名" required>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">科目代碼</label>
                        <div class="control">
                            <input class="input" type="text" name="subject_code" value="001">
                        </div>
                    </div>

                    <div class="field">
                        <div class="control">
                            <label class="checkbox">
                                <input type="checkbox" name="is_default"> 預設帳戶
                            </label>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">備註</label>
                        <div class="control">
                            <textarea class="textarea" name="note" placeholder="請輸入備註說明"></textarea>
                        </div>
                    </div>
                    
                    <div class="field is-grouped">
                        <div class="control">
                            <button type="submit" class="button is-link">新增帳戶</button>
                        </div>
                        <div class="control">
                            <a href="/account/bank" class="button is-light">取消</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const bankSelect = document.getElementById('bank-head-select');
    const branchSelect = document.getElementById('bank-branch-select');
    const otherBankField = document.getElementById('otherBankField');
    const otherBankInput = document.querySelector('input[name="other_bank_name"]');

    // 初始化 Choices.js
    const bankChoices = new Choices(bankSelect, { searchEnabled: true, itemSelectText: '' });
    const branchChoices = new Choices(branchSelect, { searchEnabled: true, itemSelectText: '' });

    // 取得總行
    fetch('/api/bank_heads')
      .then(res => res.json())
      .then(heads => {
        bankChoices.clearChoices();
        bankChoices.setChoices(
          Object.entries(heads).map(([code, name]) => ({
            value: code,
            label: `${code} - ${name}`
          })),
          'value',
          'label',
          false
        );
      });

    // 當總行選擇改變時，載入分行
    bankSelect.addEventListener('change', function() {
      const headCode = this.value;
      branchChoices.clearChoices();
      if (!headCode) return;
      fetch('/api/bank_branches/' + headCode)
        .then(res => res.json())
        .then(branches => {
          branchChoices.setChoices(
            branches.map(branch => ({
              value: branch.code,
              label: `${branch.code} - ${branch.name}`
            })),
            'value',
            'label',
            false
          );
        });
      toggleOtherBankField();
    });

    function toggleOtherBankField() {
        if (bankSelect.value === '其他') {
            otherBankField.style.display = 'block';
            otherBankInput.required = true;
        } else {
            otherBankField.style.display = 'none';
            otherBankInput.required = false;
        }
    }

    // 初始化時檢查
    toggleOtherBankField();

    // 監聽選擇變更
    bankSelect.addEventListener('change', toggleOtherBankField);

    // 表單提交前處理
    document.querySelector('form').addEventListener('submit', function(e) {
        if (bankSelect.value === '其他') {
            bankSelect.value = otherBankInput.value;
        }
    });
});
</script>
</body>
</html> 