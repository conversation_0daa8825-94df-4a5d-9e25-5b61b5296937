<!-- 收付款狀態模組 -->
<div class="field">
    <div class="columns is-mobile">
        <div class="column is-narrow">
            <label class="label">收付款狀態</label>
            <div class="buttons has-addons" id="pay-btns">
                <button class="button is-selected is-primary" type="button">未收付款</button>
                <button class="button" type="button">已收付款</button>
            </div>
            <input type="hidden" name="is_paid" id="is-paid-input" value="0">
        </div>
        <div class="column">
            <label class="label">應收付日期</label>
            <div class="control has-icons-right">
                <input class="input" type="date" name="should_paid_date">
                <span class="icon is-small is-right">
                    <i class="fas fa-calendar"></i>
                </span>
            </div>
        </div>
        <div class="column" id="paid-date-field" style="display:none;">
            <label class="label">實收付日期</label>
            <div class="control has-icons-right">
                <input class="input" type="date" name="paid_date">
                <span class="icon is-small is-right">
                    <i class="fas fa-calendar"></i>
                </span>
            </div>
        </div>
    </div>
</div>

<script>
// 收付款狀態 JavaScript
(function() {
    // 收付款狀況按鈕切換
    const payBtns = document.querySelectorAll('#pay-btns button');
    const paidDateField = document.getElementById('paid-date-field');
    const isPaidInput = document.getElementById('is-paid-input');
    
    payBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            payBtns.forEach(b => { 
                b.classList.remove('is-selected','is-primary'); 
            });
            this.classList.add('is-selected','is-primary');
            
            if (this.textContent.trim() === '已收付款') {
                paidDateField.style.display = '';
                isPaidInput.value = '1';
            } else {
                paidDateField.style.display = 'none';
                isPaidInput.value = '0';
            }
        });
    });
})();
</script>