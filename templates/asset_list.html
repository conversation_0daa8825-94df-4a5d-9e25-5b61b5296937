<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>財產列表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f5f6fa;
        }

        .column.is-narrow {
            flex: none;
            width: 200px !important;
            max-width: 200px !important;
            min-width: 200px !important;
        }

        .sidebar {
            max-width: 200px;
            min-width: 180px;
            width: 200px;
        }

        .sidebar .menu-label {
            font-size: 14px;
            font-weight: bold;
        }

        .sidebar .menu-list a {
            font-size: 20px;
            padding: 0.5em 0.75em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .main-content {
            padding: 2rem 1rem;
        }

        .header-section {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .back-link {
            font-size: 1.5rem;
            color: #363636;
            text-decoration: none;
            margin-right: 1rem;
            font-weight: 600;
        }

        .back-link:hover {
            color: #3273dc;
        }

        .header-buttons {
            margin-left: auto;
            display: flex;
            gap: 0.5rem;
        }

        .main-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
        }

        .tab-bar {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0;
        }

        .tab-bar .tab-active {
            color: #2563eb;
            border-bottom: 3px solid #2563eb;
            background: #e5edfa;
            padding: 0.5rem 1rem;
            border-radius: 6px 6px 0 0;
        }

        .tab-bar .tab-inactive {
            color: #bfc7d1;
            padding: 0.5rem 1rem;
        }

        .asset-table th {
            background: #3273dc;
            color: #fff;
            text-align: center;
            font-weight: 600;
            border: none;
            padding: 1rem 0.75rem;
        }

        .asset-table td {
            text-align: center;
            padding: 0.75rem;
            vertical-align: middle;
            border-bottom: 1px solid #dbdbdb;
        }

        .asset-table {
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 0;
        }

        .asset-table tbody tr:hover {
            background-color: #fafafa;
        }

        .page-btns {
            float: right;
        }

        .asset-card {
            background: #fff;
            border-radius: 6px;
            box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
            padding: 0;
        }

        .table-container {
            background: white;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="main-content">
                    <div class="header-section">
                        <a href="/?main=資產管理" class="back-link">← 財產列表</a>
                        <div class="header-buttons">
                            <button class="button is-info is-light">頁面指南</button>
                            <a class="button is-link" href="/add_prepaid_expense">新增預付費用</a>
                        </div>
                    </div>
                    <div class="asset-card p-5">
                        <div class="tab-bar mb-0 pb-0" style="display:flex;gap:1.5em;">
                            <span class="tab-active">預付費用</span>
                            <span class="tab-inactive">｜ 各項攤提</span>
                            <span class="tab-inactive">｜ 固定資產</span>
                            <span class="tab-inactive">｜ 非固定資產</span>
                            <span class="tab-inactive">｜ 已出售資產</span>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="table asset-table is-fullwidth">
                            <thead>
                                <tr>
                                    <th>記帳日期</th>
                                    <th>分類</th>
                                    <th>名稱</th>
                                    <th>應付日期</th>
                                    <th>實付日期</th>
                                    <th>使用年數</th>
                                    <th>起算日</th>
                                    <th>總金額(未稅)</th>
                                    <th>上期認列數</th>
                                    <th>本期認列數</th>
                                    <th>預付費用餘額</th>
                                    <th>憑證預覽</th>
                                    <th>動作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 資料列可動態產生 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</body>

</html>