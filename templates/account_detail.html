<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>帳戶明細</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      background: #f5f6fa;
    }

    .column.is-narrow {
      flex: none;
      width: 200px !important;
      max-width: 200px !important;
      min-width: 200px !important;
    }

    .sidebar {
      max-width: 200px;
      min-width: 180px;
      width: 200px;
    }

    .sidebar .menu-label {
      font-size: 14px;
      font-weight: bold;
    }

    .sidebar .menu-list a {
      font-size: 20px;
      padding: 0.5em 0.75em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .main-content {
      padding: 2rem 1rem;
    }

    .header-section {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;
    }

    .back-link {
      font-size: 1.5rem;
      color: #363636;
      text-decoration: none;
      margin-right: 1rem;
      font-weight: 600;
    }

    .back-link:hover {
      color: #3273dc;
    }

    .header-buttons {
      margin-left: auto;
      display: flex;
      gap: 0.5rem;
    }

    .account-selector {
      background: white;
      padding: 1rem;
      border-radius: 6px;
      margin-bottom: 1rem;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .account-info-card {
      background: white;
      border-radius: 6px;
      padding: 1.5rem;
      margin-bottom: 1rem;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
    }

    .account-name {
      font-size: 1.2rem;
      font-weight: 600;
      color: #363636;
      margin-bottom: 0.5rem;
    }

    .account-number {
      color: #666;
      margin-bottom: 1rem;
    }

    .balance-section {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 1rem;
      border-radius: 6px;
      text-align: center;
    }

    .balance-label {
      font-size: 0.9rem;
      opacity: 0.9;
      margin-bottom: 0.5rem;
    }

    .balance-amount {
      font-size: 2rem;
      font-weight: 700;
    }

    .transactions-table {
      background: white;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
    }

    .table {
      margin-bottom: 0;
    }

    .table th {
      background-color: #3273dc;
      color: white;
      font-weight: 600;
      border: none;
      padding: 1rem 0.75rem;
      text-align: center;
      vertical-align: middle;
      font-size: 0.9rem;
    }

    .table td {
      padding: 0.75rem;
      text-align: center;
      vertical-align: middle;
      border-bottom: 1px solid #dbdbdb;
      font-size: 0.9rem;
    }

    .table tbody tr:hover {
      background-color: #fafafa;
    }

    .amount-in {
      color: #48c774;
      font-weight: 600;
    }

    .amount-out {
      color: #f14668;
      font-weight: 600;
    }

    .amount-neutral {
      color: #363636;
    }

    .pagination-section {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 1rem;
      background: white;
      border-radius: 0 0 6px 6px;
    }

    .no-account-selected {
      background: white;
      border-radius: 6px;
      padding: 3rem;
      text-align: center;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
    }

    .no-account-selected .icon {
      font-size: 3rem;
      color: #dbdbdb;
      margin-bottom: 1rem;
    }
  </style>
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <div class="main-content">
          <div class="header-section">
            <a href="/?main=資金管理" class="back-link">← 帳戶明細</a>
            <div class="header-buttons">
              <button class="button is-info is-light">頁面指南</button>
            </div>
          </div>

          <!-- 帳戶選擇器 -->
          <div class="account-selector">
            <div class="field">
              <label class="label">選擇帳戶：</label>
              <div class="control">
                <div class="select">
                  <select id="account-selector" onchange="selectAccount()">
                    <option value="">請選擇帳戶</option>
                    {% for account in all_accounts %}
                      <option value="{{ account.id }}" {% if account.id == selected_account_id %}selected{% endif %}>
                        {{ account.name }}{% if account.account_number %} ({{ account.account_number }}){% endif %}
                      </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
          </div>

          {% if account_info %}
            <!-- 帳戶資訊卡片 -->
            <div class="account-info-card">
              <div class="columns">
                <div class="column is-8">
                  <div class="account-name">{{ account_info.name }}</div>
                  <div class="account-number">
                    帳號：{{ account_info.account_number or '無' }}
                    {% if account_info.bank_name %}
                      | {{ account_info.bank_name }}
                    {% endif %}
                  </div>
                </div>
                <div class="column is-4">
                  <div class="balance-section">
                    <div class="balance-label">目前餘額</div>
                    <div class="balance-amount">{{ "{:,}".format(account_info.balance) }}元</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 交易記錄表格 -->
            <div class="transactions-table">
              <table class="table is-fullwidth">
                <thead>
                  <tr>
                    <th>交易日期</th>
                    <th>收入金額</th>
                    <th>支出金額</th>
                    <th>餘額</th>
                    <th>摘要</th>
                    <th>備註</th>
                    <th>憑證</th>
                  </tr>
                </thead>
                <tbody>
                  {% if transactions %}
                    {% for transaction in transactions %}
                    <tr>
                      <td>{{ transaction.date }}</td>
                      <td>
                        {% if transaction.amount_in > 0 %}
                          <span class="amount-in">{{ "{:,}".format(transaction.amount_in) }}</span>
                        {% else %}
                          -
                        {% endif %}
                      </td>
                      <td>
                        {% if transaction.amount_out > 0 %}
                          <span class="amount-out">{{ "{:,}".format(transaction.amount_out) }}</span>
                        {% else %}
                          -
                        {% endif %}
                      </td>
                      <td>
                        <span class="amount-neutral">{{ "{:,}".format(transaction.balance) }}</span>
                      </td>
                      <td>{{ transaction.description }}</td>
                      <td>{{ transaction.note or '-' }}</td>
                      <td>
                        {% if transaction.voucher %}
                          <span class="tag is-info is-light">{{ transaction.voucher }}</span>
                        {% else %}
                          -
                        {% endif %}
                      </td>
                    </tr>
                    {% endfor %}
                  {% else %}
                    <tr>
                      <td colspan="7" class="has-text-grey">此帳戶目前沒有交易記錄</td>
                    </tr>
                  {% endif %}
                </tbody>
              </table>
              
              <!-- 分頁 -->
              <div class="pagination-section">
                <nav class="pagination is-small" role="navigation" aria-label="pagination">
                  <a class="pagination-previous" disabled>上一頁</a>
                  <a class="pagination-next" disabled>下一頁</a>
                  <ul class="pagination-list">
                    <li><a class="pagination-link is-current" aria-label="第1頁" aria-current="page">1</a></li>
                  </ul>
                </nav>
                <span class="has-text-grey is-size-7 ml-3">共 1 頁，2 筆記錄</span>
              </div>
            </div>
          {% else %}
            <!-- 未選擇帳戶時的提示 -->
            <div class="no-account-selected">
              <div class="icon">
                <i class="fas fa-university"></i>
              </div>
              <h3 class="title is-4 has-text-grey">請選擇要查看的帳戶</h3>
              <p class="has-text-grey">從上方下拉選單中選擇一個帳戶來查看其明細記錄</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
  <script>
    function selectAccount() {
      const selector = document.getElementById('account-selector');
      const accountId = selector.value;
      
      if (accountId) {
        // 重新載入頁面並帶上選擇的帳戶ID
        window.location.href = `/account_detail?account_id=${accountId}`;
      } else {
        // 清除帳戶選擇
        window.location.href = '/account_detail';
      }
    }
  </script>
</body>

</html>
