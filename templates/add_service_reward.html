<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <title>新增勞務報酬單</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <style>
    .main-title {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 1.5rem;
    }

    .form-section {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px #eee;
      padding: 2rem;
      margin-bottom: 2rem;
    }

    .field {
      margin-bottom: 1.2rem;
    }

    .is-required:after {
      content: '*';
      color: #e53e3e;
      margin-left: 0.2em;
    }

    .form-btns {
      display: flex;
      justify-content: flex-end;
      gap: 1.5em;
      margin-top: 2.5em;
    }

    /* 分頁樣式 */
    .tabs.is-toggle li.is-active a {
      background-color: #3273dc;
      border-color: #3273dc;
      color: #fff;
    }

    /* 個人填寫表單的特殊樣式 */
    #tab-content-personal .form-section {
      border-left: 4px solid #48c774;
    }

    #tab-content-personal .form-section::before {
      content: "個人填寫（無需身份證號和戶籍地址）";
      display: block;
      background: #48c774;
      color: white;
      padding: 0.5rem 1rem;
      margin: -2rem -2rem 1.5rem -2rem;
      border-radius: 8px 8px 0 0;
      font-weight: bold;
      font-size: 0.9rem;
    }

    /* 公司填寫表單的特殊樣式 */
    #tab-content-company .form-section {
      border-left: 4px solid #3273dc;
    }

    #tab-content-company .form-section::before {
      content: "公司填寫（需要完整資料）";
      display: block;
      background: #3273dc;
      color: white;
      padding: 0.5rem 1rem;
      margin: -2rem -2rem 1.5rem -2rem;
      border-radius: 8px 8px 0 0;
      font-weight: bold;
      font-size: 0.9rem;
    }
  </style>
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <!-- 頁面標題 -->
        <div class="mb-4">
          <h1 class="title is-4">
            <a href="javascript:history.back()"
              style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
            新增勞務報酬單
          </h1>
        </div>
        <div class="tabs is-toggle is-centered mb-4">
          <ul id="reward-tabs">
            <li class="is-active" data-tab="company"><a>公司填寫</a></li>
            <li data-tab="personal"><a>個人填寫</a></li>
          </ul>
        </div>
        <div id="tab-content-company">
          <form method="post" class="form-section" enctype="multipart/form-data">
            <div style="font-weight:bold; font-size:1.1em; margin-bottom:1.5em;">基本資料</div>
            <div class="columns is-multiline">
              <div class="column is-2">
                <div class="field">
                  <label class="label is-required">建立日期</label>
                  <input class="input" type="date" name="create_date" required>
                </div>
              </div>
              <div class="column is-1">
                <div class="field">
                  <label class="label is-required">
                    姓名
                    <span class="icon has-text-info" title="請輸入或搜尋人員" style="vertical-align: middle;">
                      <i class="fas fa-question-circle"></i>
                    </span>
                  </label>
                  <input class="input" type="text" name="name" placeholder="請輸入或搜尋人員" required>
                </div>
              </div>
              <div class="column is-2">
                <div class="field">
                  <label class="label is-required">身份證號</label>
                  <input class="input" type="text" name="id_number" placeholder="請輸入身份證號" required>
                </div>
              </div>
              <div class="column is-3">
                <div class="field">
                  <label class="label is-required">戶籍地址</label>
                  <input class="input" type="text" name="address" placeholder="請輸入戶籍地址" required>
                </div>
              </div>
              <div class="column is-2">
                <div class="field">
                  <label class="label is-required">電子信箱</label>
                  <input class="input" type="email" name="email" placeholder="請輸入電子信箱" required>
                </div>
              </div>
              <div class="column is-2">
                <div class="field">
                  <label class="label">連絡電話</label>
                  <input class="input" type="text" name="phone" placeholder="請輸入連絡電話">
                </div>
              </div>
            </div>
            <div style="font-weight:bold; font-size:1.1em; margin-bottom:1.5em; margin-top:2em;">勞務資訊</div>
            <div class="columns is-multiline">
              <div class="column is-2">
                <div class="field">
                  <label class="label is-required">
                    國籍
                    <span class="icon has-text-info" title="國籍說明" style="vertical-align: middle;">
                      <i class="fas fa-question-circle"></i>
                    </span>
                  </label>
                  <div class="select is-fullwidth">
                    <select name="nationality" required>
                      <option value="本國人">本國人</option>
                      <option value="外國人">外國人(居住滿183天)</option>
                      <option value="外國人">外國人(未滿183天)</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="column is-1">
                <div class="field">
                  <label class="label is-required">免扣健保</label>
                  <div class="select is-fullwidth">
                    <select name="no_health_insurance" required>
                      <option value="是">是</option>
                      <option value="否">否</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="column is-2">
                <div class="field">
                  <label class="label is-required">
                    申報類別
                    <span class="icon has-text-info" title="申報類別說明" style="vertical-align: middle;">
                      <i class="fas fa-question-circle"></i>
                    </span>
                  </label>
                  <div class="select is-fullwidth">
                    <select name="declaration_type" required>
                      <option value="9A">9A 執行業務所得</option>
                      <option value="9B">9B 稿費</option>
                      <option value="50">50 薪資所得</option>
                      <option value="92">92 其他所得</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="column is-2">
                <div class="field">
                  <label class="label is-required">勞報確認</label>
                  <div class="select is-fullwidth">
                    <select name="confirmation_method" required>
                      <option value="online">線上確認</option>
                      <option value="paper">紙本確認</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="column is-5">
                <div class="field">
                  <label class="label is-required">勞務期間</label>
                  <div class="is-flex is-align-items-center">
                    <input class="input" type="date" name="service_start_date" placeholder="起始日" required
                      style="max-width: 240px;">
                    <span style="margin: 0 8px;">~</span>
                    <input class="input" type="date" name="service_end_date" placeholder="結束日" required
                      style="max-width: 240px;">
                  </div>
                </div>
              </div>
            </div>
            <div class="columns is-multiline">
              <div class="column is-12">
                <div class="field">
                  <label class="label is-required">勞務內容</label>
                  <input class="input" type="text" name="service_content" placeholder="請輸入勞務內容" required>
                </div>
              </div>
            </div>
            <div style="font-weight:bold; font-size:1.1em; margin-bottom:1.5em; margin-top:2em;">付款資訊</div>
            <div class="columns">
              <div class="column is-4">
                <div class="field">
                  <label class="label is-required">
                    付款資金帳戶
                    <span style="font-size:0.9em; color:#d9534f;">＊（依付款方式選現金或匯款帳戶）</span>
                  </label>
                  <div class="select is-fullwidth">
                    <select name="payment_account" required>
                      <option value="">請選擇付款資金帳戶</option>
                      {% for acc in accounts %}
                      <option value="{{ acc.id }}">
                        {{ acc.name }}
                        {% if acc.category == '銀行帳戶' and acc.account_number %}
                        ({{ acc.account_number }})
                        {% endif %}
                      </option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label is-required">應付日期</label>
                  <input class="input" type="date" name="payable_date" required>
                </div>
              </div>
              <div class="column is-4">
                <div class="field is-grouped is-align-items-center">
                  <div class="control is-expanded">
                    <label class="label is-required">付款金額</label>
                    <input class="input" type="number" name="payment_amount" min="0" value="0" required>
                  </div>
                  <div class="control" style="margin-top:2em;">
                    <label class="checkbox">
                      <input type="checkbox" name="actual_amount"> 實領金額
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <div style="border-top:1px solid #ddd; margin:1em 0;"></div>
            <div class="columns is-mobile">
              <div class="column is-4">支領金額</div>
              <div class="column is-4">代扣所得金額</div>
              <div class="column is-4">健保金額</div>
            </div>
            <div class="columns is-mobile">
              <div class="column is-4"><span id="display_pay_amount">0</span> 元</div>
              <div class="column is-4"><span id="display_tax_amount">0</span> 元</div>
              <div class="column is-4"><span id="display_health_amount">0</span> 元</div>
            </div>
            <div style="border-top:1px solid #222; margin:1em 0;"></div>
            <div class="columns is-mobile">
              <div class="column is-8" style="font-weight:bold;">實際支付金額</div>
              <div class="column is-4" style="font-weight:bold;"><span id="display_actual_amount">0</span> 元</div>
            </div>
            <div style="font-weight:bold; font-size:1.1em; margin-bottom:1.5em; margin-top:2em;">所有人提供資料</div>
            <div class="columns">
              <div class="column is-4">
                <div class="field">
                  <label class="label">所有人銀行代碼</label>
                  <input class="input" type="text" name="owner_bank_code" placeholder="請輸入銀行代碼">
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label">所有人銀行帳號</label>
                  <input class="input" type="text" name="owner_bank_account" placeholder="請輸入銀行帳號">
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label">所有人戶名</label>
                  <input class="input" type="text" name="owner_account_name" placeholder="請輸入所有人戶名">
                </div>
              </div>
            </div>
            <div class="columns">
              <div class="column is-4">
                <div class="field">
                  <label class="label">所有人存摺封面</label>
                  <div class="file has-name is-boxed is-fullwidth">
                    <label class="file-label">
                      <input class="file-input" type="file" name="owner_passbook" accept=".jpg,.jpeg,.png">
                      <span class="file-cta">
                        <span class="file-icon"><i class="fas fa-upload"></i></span>
                        <span class="file-label">上傳圖片</span>
                      </span>
                    </label>
                  </div>
                  <p class="help">請選擇JPG/PNG檔案，大小請於5MB以內，標準格式為JPG、PNG</p>
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label">所有人身分證正面</label>
                  <div class="file has-name is-boxed is-fullwidth">
                    <label class="file-label">
                      <input class="file-input" type="file" name="owner_id_front" accept=".jpg,.jpeg,.png">
                      <span class="file-cta">
                        <span class="file-icon"><i class="fas fa-upload"></i></span>
                        <span class="file-label">上傳圖片</span>
                      </span>
                    </label>
                  </div>
                  <p class="help">請選擇JPG/PNG檔案，大小請於5MB以內，標準格式為JPG、PNG</p>
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label">所有人身分證反面</label>
                  <div class="file has-name is-boxed is-fullwidth">
                    <label class="file-label">
                      <input class="file-input" type="file" name="owner_id_back" accept=".jpg,.jpeg,.png">
                      <span class="file-cta">
                        <span class="file-icon"><i class="fas fa-upload"></i></span>
                        <span class="file-label">上傳圖片</span>
                      </span>
                    </label>
                  </div>
                  <p class="help">請選擇JPG/PNG檔案，大小請於5MB以內，標準格式為JPG、PNG</p>
                </div>
              </div>
            </div>
            <div style="font-weight:bold; font-size:1.1em; margin-bottom:1.5em; margin-top:2em;">其他資訊</div>
            <div class="columns">
              <div class="column is-4">
                <div class="field">
                  <label class="label">部門別</label>
                  <div class="select is-fullwidth">
                    <select name="department_id">
                      <option value="">請選擇部門</option>
                      {% for dept in departments %}
                      <option value="{{ dept.id }}">{{ dept.name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label">專案別</label>
                  <div class="select is-fullwidth">
                    <select name="project_id">
                      <option value="">請選擇專案</option>
                      {% for proj in projects %}
                      <option value="{{ proj.id }}">{{ proj.name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="columns">
              <div class="column is-6">
                <div class="field">
                  <label class="label">注意事項（預設建議內容可修改）</label>
                  <div class="control">
                    <textarea class="textarea" name="notice"
                      style="background:#f8f8f8;">請務必確認所有填寫內容是否正確，如有錯誤，責任需由所有人自行承擔。</textarea>
                  </div>
                </div>
              </div>
              <div class="column is-6">
                <div class="field">
                  <label class="label">備註</label>
                  <div class="control">
                    <textarea class="textarea" name="note" placeholder="請輸入備註"></textarea>
                  </div>
                </div>
              </div>
            </div>
            <div class="field is-grouped is-grouped-right" style="margin-top:2em;">
              <div class="control">
                <button class="button is-light" type="submit" name="action" value="save_exit">暫存後離開</button>
              </div>
              <div class="control">
                <button class="button is-primary" type="submit" name="action" value="submit">建立完成</button>
              </div>
            </div>
            <!-- 添加隱藏欄位來標識這是公司填寫 -->
            <input type="hidden" name="form_type" value="company">
          </form>
        </div>
        <div id="tab-content-personal" style="display:none;">
          <form method="post" class="form-section" enctype="multipart/form-data">
            <div style="font-weight:bold; font-size:1.1em; margin-bottom:1.5em;">基本資料</div>
            <div class="columns is-multiline">
              <div class="column is-2">
                <div class="field">
                  <label class="label is-required">建立日期</label>
                  <input class="input" type="date" name="create_date" required>
                </div>
              </div>
              <div class="column is-3">
                <div class="field">
                  <label class="label is-required">
                    姓名
                    <span class="icon has-text-info" title="請輸入或搜尋人員" style="vertical-align: middle;">
                      <i class="fas fa-question-circle"></i>
                    </span>
                  </label>
                  <input class="input" type="text" name="name" placeholder="請輸入或搜尋人員" required>
                </div>
              </div>
              <!-- 移除身份證號欄位 -->
              <!-- 移除戶籍地址欄位 -->
              <div class="column is-3">
                <div class="field">
                  <label class="label is-required">電子信箱</label>
                  <input class="input" type="email" name="email" placeholder="請輸入電子信箱" required>
                </div>
              </div>
              <div class="column is-2">
                <div class="field">
                  <label class="label">連絡電話</label>
                  <input class="input" type="text" name="phone" placeholder="請輸入連絡電話">
                </div>
              </div>
            </div>
            <div style="font-weight:bold; font-size:1.1em; margin-bottom:1.5em; margin-top:2em;">勞務資訊</div>
            <div class="columns is-multiline">
              <div class="column is-2">
                <div class="field">
                  <label class="label is-required">
                    國籍
                    <span class="icon has-text-info" title="國籍說明" style="vertical-align: middle;">
                      <i class="fas fa-question-circle"></i>
                    </span>
                  </label>
                  <div class="select is-fullwidth">
                    <select name="nationality" required>
                      <option value="本國人">本國人</option>
                      <option value="外國人">外國人(居住滿183天)</option>
                      <option value="外國人">外國人(未滿183天)</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="column is-1">
                <div class="field">
                  <label class="label is-required">免扣健保</label>
                  <div class="select is-fullwidth">
                    <select name="no_health_insurance" required>
                      <option value="是">是</option>
                      <option value="否">否</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="column is-2">
                <div class="field">
                  <label class="label is-required">
                    申報類別
                    <span class="icon has-text-info" title="申報類別說明" style="vertical-align: middle;">
                      <i class="fas fa-question-circle"></i>
                    </span>
                  </label>
                  <div class="select is-fullwidth">
                    <select name="declaration_type" required>
                      <option value="9A">9A 執行業務所得</option>
                      <option value="9B">9B 稿費</option>
                      <option value="50">50 薪資所得</option>
                      <option value="92">92 其他所得</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="column is-2">
                <div class="field">
                  <label class="label is-required">勞報確認</label>
                  <div class="select is-fullwidth">
                    <select name="confirmation_method" required>
                      <option value="online">線上確認</option>
                      <option value="paper">紙本確認</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="column is-5">
                <div class="field">
                  <label class="label is-required">勞務期間</label>
                  <div class="is-flex is-align-items-center">
                    <input class="input" type="date" name="service_start_date" placeholder="起始日" required
                      style="max-width: 240px;">
                    <span style="margin: 0 8px;">~</span>
                    <input class="input" type="date" name="service_end_date" placeholder="結束日" required
                      style="max-width: 240px;">
                  </div>
                </div>
              </div>
            </div>
            <div class="columns is-multiline">
              <div class="column is-12">
                <div class="field">
                  <label class="label is-required">勞務內容</label>
                  <input class="input" type="text" name="service_content" placeholder="請輸入勞務內容" required>
                </div>
              </div>
            </div>
            <div style="font-weight:bold; font-size:1.1em; margin-bottom:1.5em; margin-top:2em;">付款資訊</div>
            <div class="columns">
              <div class="column is-4">
                <div class="field">
                  <label class="label is-required">
                    付款資金帳戶
                    <span style="font-size:0.9em; color:#d9534f;">＊（依付款方式選現金或匯款帳戶）</span>
                  </label>
                  <div class="select is-fullwidth">
                    <select name="payment_account" required>
                      <option value="">請選擇付款資金帳戶</option>
                      {% for acc in accounts %}
                      <option value="{{ acc.id }}">
                        {{ acc.name }}
                        {% if acc.category == '銀行帳戶' and acc.account_number %}
                        ({{ acc.account_number }})
                        {% endif %}
                      </option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label is-required">應付日期</label>
                  <input class="input" type="date" name="payable_date" required>
                </div>
              </div>
              <div class="column is-4">
                <div class="field is-grouped is-align-items-center">
                  <div class="control is-expanded">
                    <label class="label is-required">付款金額</label>
                    <input class="input" type="number" name="payment_amount" min="0" value="0" required>
                  </div>
                  <div class="control" style="margin-top:2em;">
                    <label class="checkbox">
                      <input type="checkbox" name="actual_amount"> 實領金額
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <div style="border-top:1px solid #ddd; margin:1em 0;"></div>
            <div class="columns is-mobile">
              <div class="column is-4">支領金額</div>
              <div class="column is-4">代扣所得金額</div>
              <div class="column is-4">健保金額</div>
            </div>
            <div class="columns is-mobile">
              <div class="column is-4"><span id="display_pay_amount_personal">0</span> 元</div>
              <div class="column is-4"><span id="display_tax_amount_personal">0</span> 元</div>
              <div class="column is-4"><span id="display_health_amount_personal">0</span> 元</div>
            </div>
            <div style="border-top:1px solid #222; margin:1em 0;"></div>
            <div class="columns is-mobile">
              <div class="column is-8" style="font-weight:bold;">實際支付金額</div>
              <div class="column is-4" style="font-weight:bold;"><span id="display_actual_amount_personal">0</span> 元
              </div>
            </div>
            <div style="font-weight:bold; font-size:1.1em; margin-bottom:1.5em; margin-top:2em;">所有人提供資料</div>
            <div class="columns">
              <div class="column is-4">
                <div class="field">
                  <label class="label">所有人銀行代碼</label>
                  <input class="input" type="text" name="owner_bank_code" placeholder="請輸入銀行代碼">
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label">所有人銀行帳號</label>
                  <input class="input" type="text" name="owner_bank_account" placeholder="請輸入銀行帳號">
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label">所有人戶名</label>
                  <input class="input" type="text" name="owner_account_name" placeholder="請輸入所有人戶名">
                </div>
              </div>
            </div>
            <div class="columns">
              <div class="column is-4">
                <div class="field">
                  <label class="label">所有人存摺封面</label>
                  <div class="file has-name is-boxed is-fullwidth">
                    <label class="file-label">
                      <input class="file-input" type="file" name="owner_passbook" accept=".jpg,.jpeg,.png">
                      <span class="file-cta">
                        <span class="file-icon"><i class="fas fa-upload"></i></span>
                        <span class="file-label">上傳圖片</span>
                      </span>
                    </label>
                  </div>
                  <p class="help">請選擇JPG/PNG檔案，大小請於5MB以內，標準格式為JPG、PNG</p>
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label">所有人身分證正面</label>
                  <div class="file has-name is-boxed is-fullwidth">
                    <label class="file-label">
                      <input class="file-input" type="file" name="owner_id_front" accept=".jpg,.jpeg,.png">
                      <span class="file-cta">
                        <span class="file-icon"><i class="fas fa-upload"></i></span>
                        <span class="file-label">上傳圖片</span>
                      </span>
                    </label>
                  </div>
                  <p class="help">請選擇JPG/PNG檔案，大小請於5MB以內，標準格式為JPG、PNG</p>
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label">所有人身分證反面</label>
                  <div class="file has-name is-boxed is-fullwidth">
                    <label class="file-label">
                      <input class="file-input" type="file" name="owner_id_back" accept=".jpg,.jpeg,.png">
                      <span class="file-cta">
                        <span class="file-icon"><i class="fas fa-upload"></i></span>
                        <span class="file-label">上傳圖片</span>
                      </span>
                    </label>
                  </div>
                  <p class="help">請選擇JPG/PNG檔案，大小請於5MB以內，標準格式為JPG、PNG</p>
                </div>
              </div>
            </div>
            <div style="font-weight:bold; font-size:1.1em; margin-bottom:1.5em; margin-top:2em;">其他資訊</div>
            <div class="columns">
              <div class="column is-4">
                <div class="field">
                  <label class="label">部門別</label>
                  <div class="select is-fullwidth">
                    <select name="department_id">
                      <option value="">請選擇部門</option>
                      {% for dept in departments %}
                      <option value="{{ dept.id }}">{{ dept.name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label">專案別</label>
                  <div class="select is-fullwidth">
                    <select name="project_id">
                      <option value="">請選擇專案</option>
                      {% for proj in projects %}
                      <option value="{{ proj.id }}">{{ proj.name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="columns">
              <div class="column is-6">
                <div class="field">
                  <label class="label">注意事項（預設建議內容可修改）</label>
                  <div class="control">
                    <textarea class="textarea" name="notice"
                      style="background:#f8f8f8;">請務必確認所有填寫內容是否正確，如有錯誤，責任需由所有人自行承擔。</textarea>
                  </div>
                </div>
              </div>
              <div class="column is-6">
                <div class="field">
                  <label class="label">備註</label>
                  <div class="control">
                    <textarea class="textarea" name="note" placeholder="請輸入備註"></textarea>
                  </div>
                </div>
              </div>
            </div>
            <div class="field is-grouped is-grouped-right" style="margin-top:2em;">
              <div class="control">
                <button class="button is-light" type="submit" name="action" value="save_exit">暫存後離開</button>
              </div>
              <div class="control">
                <button class="button is-primary" type="submit" name="action" value="submit">建立完成</button>
              </div>
            </div>
            <!-- 添加隱藏欄位來標識這是個人填寫 -->
            <input type="hidden" name="form_type" value="personal">
          </form>
        </div>
        <script>
          document.addEventListener('DOMContentLoaded', function () {
            const tabs = document.querySelectorAll('#reward-tabs li');
            const company = document.getElementById('tab-content-company');
            const personal = document.getElementById('tab-content-personal');
            tabs.forEach(tab => {
              tab.addEventListener('click', function () {
                tabs.forEach(t => t.classList.remove('is-active'));
                this.classList.add('is-active');
                if (this.dataset.tab === 'company') {
                  company.style.display = '';
                  personal.style.display = 'none';
                } else {
                  company.style.display = 'none';
                  personal.style.display = '';
                }
              });
            });

            // 為個人填寫表單添加金額計算功能
            const personalForm = document.querySelector('#tab-content-personal form');
            if (personalForm) {
              const personalAmountInput = personalForm.querySelector('input[name="payment_amount"]');
              const personalActualCheckbox = personalForm.querySelector('input[name="actual_amount"]');

              function updatePersonalAmounts() {
                const amount = parseFloat(personalAmountInput.value) || 0;
                const isActual = personalActualCheckbox.checked;

                let payAmount, taxAmount, healthAmount, actualAmount;

                if (isActual) {
                  // 如果是實領金額，需要反推
                  actualAmount = amount;
                  // 簡化計算，實際應根據稅率計算
                  taxAmount = Math.round(amount * 0.1);
                  healthAmount = Math.round(amount * 0.02);
                  payAmount = actualAmount + taxAmount + healthAmount;
                } else {
                  // 如果是應付金額
                  payAmount = amount;
                  taxAmount = Math.round(amount * 0.1);
                  healthAmount = Math.round(amount * 0.02);
                  actualAmount = payAmount - taxAmount - healthAmount;
                }

                document.getElementById('display_pay_amount_personal').textContent = payAmount.toLocaleString();
                document.getElementById('display_tax_amount_personal').textContent = taxAmount.toLocaleString();
                document.getElementById('display_health_amount_personal').textContent = healthAmount.toLocaleString();
                document.getElementById('display_actual_amount_personal').textContent = actualAmount.toLocaleString();
              }

              personalAmountInput.addEventListener('input', updatePersonalAmounts);
              personalActualCheckbox.addEventListener('change', updatePersonalAmounts);

              // 初始計算
              updatePersonalAmounts();
            }
          });
        </script>
      </div>
    </div>
  </div>
  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</body>

</html>