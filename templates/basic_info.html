<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公司基本資料</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        .info-label {
            width: 120px;
            text-align: right;
        }

        .is-readonly {
            background: #f5f5f5;
        }

        .container-box {
            max-width: 700px;
            margin: 40px auto;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=設定"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        公司基本資料
                    </h1>
                </div>
                <div class="container-box">
                    <form method="post">
                        <div class="field is-horizontal">
                            <div class="field-label is-normal info-label">
                                <label class="label">公司名稱<span class="has-text-danger">*</span></label>
                            </div>
                            <div class="field-body">
                                <div class="field">
                                    <div class="control">
                                        <input class="input is-readonly" name="company_name" type="text"
                                            value="{{ company_info.company_name if company_info else '量心醫藥有限公司' }}"
                                            readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="field is-horizontal">
                            <div class="field-label is-normal info-label">
                                <label class="label">公司統編 <span class="icon has-text-info is-small"><i
                                            class="fas fa-info-circle"></i></span></label>
                            </div>
                            <div class="field-body">
                                <div class="field">
                                    <div class="control">
                                        <input class="input is-readonly" name="company_id" type="text"
                                            value="{{ company_info.company_id if company_info else '90655676' }}"
                                            readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="field is-horizontal">
                            <div class="field-label is-normal info-label">
                                <label class="label">負責人姓名<span class="has-text-danger">*</span></label>
                            </div>
                            <div class="field-body">
                                <div class="field">
                                    <div class="control">
                                        <input class="input is-readonly" name="owner_name" type="text"
                                            value="{{ company_info.owner_name if company_info else '高藥師' }}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="field is-horizontal">
                            <div class="field-label is-normal info-label">
                                <label class="label">負責人聯絡電話<span class="has-text-danger">*</span></label>
                            </div>
                            <div class="field-body">
                                <div class="field">
                                    <div class="control">
                                        <input class="input is-readonly" name="owner_phone" type="text"
                                            value="{{ company_info.owner_phone if company_info else '0980347570' }}"
                                            readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="field is-horizontal">
                            <div class="field-label is-normal info-label">
                                <label class="label">電子信箱<span class="has-text-danger">*</span></label>
                            </div>
                            <div class="field-body">
                                <div class="field">
                                    <div class="control">
                                        <input class="input is-readonly" name="email" type="email"
                                            value="{{ company_info.email if company_info else '<EMAIL>' }}"
                                            readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="field is-horizontal">
                            <div class="field-label is-normal info-label">
                                <label class="label">稅徵機關名稱</label>
                            </div>
                            <div class="field-body">
                                <div class="field">
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="tax_office" id="tax_office_select" disabled>
                                                <option value="">請選擇稅徵機關</option>
                                                {% for office in tax_offices %}
                                                <option value="{{ office }}" {% if company_info and
                                                    company_info.tax_office==office %}selected{% endif %}>{{ office }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="field is-horizontal">
                            <div class="field-label is-normal info-label">
                                <label class="label">營業地址</label>
                            </div>
                            <div class="field-body">
                                <div class="field">
                                    <div class="control">
                                        <input class="input is-readonly" name="address" type="text"
                                            value="{{ company_info.address if company_info else '台南市東區崇學路165號5F' }}"
                                            readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="field is-horizontal">
                            <div class="field-label is-normal info-label">
                                <label class="label">扣繳申報聯絡人姓名</label>
                            </div>
                            <div class="field-body">
                                <div class="field">
                                    <div class="control">
                                        <input class="input is-readonly" name="contact_name" type="text"
                                            value="{{ company_info.contact_name if company_info else '高啟峰' }}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="field is-horizontal">
                            <div class="field-label is-normal info-label">
                                <label class="label">扣繳申報聯絡人電話</label>
                            </div>
                            <div class="field-body">
                                <div class="field">
                                    <div class="control">
                                        <input class="input is-readonly" name="contact_phone" type="text"
                                            value="{{ company_info.contact_phone if company_info else '0980347570' }}"
                                            readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="field is-horizontal">
                            <div class="field-label is-normal info-label">
                                <label class="label">稅籍編號</label>
                            </div>
                            <div class="field-body">
                                <div class="field">
                                    <div class="control">
                                        <input class="input is-readonly" name="tax_id" type="text"
                                            value="{{ company_info.tax_id if company_info else '*********' }}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="field is-grouped is-grouped-right mt-4">
                            <div class="control">
                                <button class="button is-link is-light" type="button" id="editBtn"
                                    onclick="toggleEdit()">編輯</button>
                            </div>
                            <div class="control">
                                <button class="button is-success" type="submit" id="saveBtn"
                                    style="display:none;">儲存</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script>
        function toggleEdit() {
            const inputs = document.querySelectorAll('.input.is-readonly');
            const editBtn = document.getElementById('editBtn');
            const saveBtn = document.getElementById('saveBtn');
            const taxOfficeSelect = document.getElementById('tax_office_select');

            if (editBtn.textContent === '編輯') {
                // 切換為編輯模式
                inputs.forEach(input => {
                    input.classList.remove('is-readonly');
                    input.readOnly = false;
                });
                // 啟用下拉選單
                if (taxOfficeSelect) {
                    taxOfficeSelect.disabled = false;
                }
                editBtn.style.display = 'none';
                saveBtn.style.display = 'inline-block';
            } else {
                // 切換為唯讀模式
                inputs.forEach(input => {
                    input.classList.add('is-readonly');
                    input.readOnly = true;
                });
                // 禁用下拉選單
                if (taxOfficeSelect) {
                    taxOfficeSelect.disabled = true;
                }
                editBtn.textContent = '編輯';
                editBtn.classList.remove('is-success');
                editBtn.classList.add('is-light');

                // 這裡可以添加儲存到後端的 AJAX 請求
                alert('資料已儲存！');
            }
        }
    </script>
</body>

</html>