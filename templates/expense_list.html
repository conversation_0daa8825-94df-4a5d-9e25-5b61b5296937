<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支出紀錄列表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        .main-title {
            font-size: 2.2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
        }

        .tab-custom {
            background: #2563eb;
            color: #fff;
            border-radius: 8px 8px 0 0;
            padding: 0.75rem 2rem;
            font-weight: bold;
            font-size: 1.2rem;
            display: inline-block;
        }

        .tab-custom.inactive {
            background: #e5e7eb;
            color: #222;
        }

        .box-shadow {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .table thead th {
            background: #2563eb !important;
            color: #fff !important;
            font-weight: 600 !important;
            border-color: #2563eb !important;
        }

        .table th,
        .table td {
            vertical-align: middle;
            text-align: center;
        }

        .is-rounded-input {
            border-radius: 999px;
        }

        .is-search-input {
            border-radius: 999px;
            border: 1px solid #e5e7eb;
            padding-left: 1.5em;
        }

        .is-search-input:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 1.5px #2563eb33;
        }

        .icon-btn {
            background: none;
            border: none;
            color: #2563eb;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=收支帳簿"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        支出紀錄列表
                    </h1>
                </div>
                <div class="mb-4">
                    <form id="search-form" method="get">
                        <div class="field is-grouped is-align-items-center">
                            <div class="control">
                                <div class="select is-rounded">
                                    <select id="date-type-select" name="date_type">
                                        <option value="a_time">記帳日期</option>
                                        <option value="should_paid_date">應收應付日期</option>
                                        <option value="paid_date">實收實付日期</option>
                                        <option value="invoice_date">發票日期</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control ml-2">
                                <input class="input is-rounded-input" type="date" id="date-start" name="date_start">
                                <span>～</span>
                                <input class="input is-rounded-input" type="date" id="date-end" name="date_end">
                            </div>
                            <div class="control ml-2">
                                <label class="checkbox">
                                    <input type="checkbox" name="hide_paid" value="1" id="hide-paid-checkbox" {% if
                                        request.args.get('hide_paid') %}checked{% endif %}> 已收付款不顯示
                                </label>
                            </div>
                            <div class="control ml-2">
                                <button class="button is-link is-light is-rounded" type="submit">搜尋</button>
                            </div>
                            <div class="control ml-auto">
                                <button class="button is-link is-light is-rounded" type="button">匯出帳務</button>
                                <button class="button is-link is-light is-rounded" type="button">匯入多筆帳務</button>
                                <a class="button is-link is-rounded ml-2" href="/expense_record">＋建立帳務</a>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="box box-shadow">
                    <div class="mb-3">
                        <a class="tab-custom inactive" href="/income_list">收入紀錄</a>
                        <span class="tab-custom">支出紀錄</span>
                    </div>
                    <div class="field is-grouped is-align-items-center mb-3">
                        <div class="control">
                            <div class="select is-rounded">
                                <select>
                                    <option>全部選取</option>
                                </select>
                            </div>
                        </div>
                        <div class="control ml-3">
                            <input class="input is-search-input" type="text" placeholder="輸入搜尋條件" style="width: 180px;">
                        </div>
                        <div class="control ml-3">
                            <button class="button is-light"><span class="icon"><i
                                        class="fas fa-search"></i></span></button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table is-fullwidth is-hoverable">
                            <thead>
                                <tr>
                                    <th>記帳時間</th>
                                    <th>總計(含稅)</th>
                                    <th>科目</th>
                                    <th>名稱</th>
                                    <th>收支對象</th>
                                    <th>資金帳戶</th>
                                    <th>收付款狀態</th>
                                    <th>應收付日期</th>
                                    <th>實收付日期</th>
                                    <th>標籤</th>
                                    <th>發票日期</th>
                                    <th>憑證圖檔</th>
                                    <th>編輯</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if expense_records %}
                                {% for record in expense_records %}
                                <tr>
                                    <td>{{ record.a_time }}</td>
                                    <td>{{ record.total }}</td>
                                    <td>{{ record.subject_name }}</td>
                                    <td>{{ record.name }}</td>
                                    <td>{{ record.payment_identity_name }}</td>
                                    <td>{{ record.account_name }}</td>
                                    <td>{{ record.payment_status }}</td>
                                    <td>{{ record.should_paid_date }}</td>
                                    <td>{{ record.paid_date }}</td>
                                    <td>{{ record.note }}</td>
                                    <td>{{ record.invoice_date }}</td>
                                    <td><span class="icon"><i class="fas fa-eye"></i></span></td>
                                    <td><button class="icon-btn" onclick="editRecord({{ record.id }})"><i
                                                class="fas fa-edit"></i></button></td>
                                </tr>
                                {% endfor %}
                                {% else %}
                                <tr>
                                    <td colspan="13" class="has-text-centered has-text-grey">目前沒有支出紀錄</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    <div class="is-flex is-justify-content-flex-end is-align-items-center mt-2">
                        <span>共 1 頁　{{ expense_records|length }}筆紀錄</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script>
        function editRecord(recordId) {
            // 跳轉到支出紀錄編輯頁面
            window.location.href = `/expense_record?edit_id=${recordId}`;
        }

        document.addEventListener('DOMContentLoaded', function () {
            // 預設本月日期
            const now = new Date();
            const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
            const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            function toDateStr(d) {
                return d.toISOString().slice(0, 10);
            }
            document.getElementById('date-start').value = toDateStr(firstDay);
            document.getElementById('date-end').value = toDateStr(lastDay);

            // 建立帳務按鈕
            const createButton = document.querySelector('button.button.is-link.is-rounded');
            if (createButton && createButton.textContent.includes('＋建立帳務')) {
                createButton.addEventListener('click', function () {
                    window.location.href = '/expense_record';
                });
            }

            // 勾選已收付款不顯示自動送出
            const hidePaidCheckbox = document.getElementById('hide-paid-checkbox');
            if (hidePaidCheckbox) {
                hidePaidCheckbox.addEventListener('change', function () {
                    document.getElementById('search-form').submit();
                });
            }
        });
    </script>
</body>

</html>