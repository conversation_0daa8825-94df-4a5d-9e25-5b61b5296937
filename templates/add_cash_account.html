<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增現金帳戶</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .container-box { max-width: 1400px; margin: 40px auto; }
        .main-card { background: #fff; border-radius: 18px; box-shadow: 0 2px 8px #eee; padding: 2.5rem 2rem; max-width: 600px; margin: 2rem auto; }
        .section-title { font-size: 1.3rem; font-weight: bold; color: #2563eb; margin-bottom: 1.2rem; }
        .back-btn { margin-bottom: 1rem; }
    </style>
</head>
<body>
<div class="container is-fluid container-box">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
        <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/account/cash"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        新增現金帳戶
                    </h1>
                </div>
            <div class="main-card">
                <div class="section-title mb-5">
                    帳戶資訊
                </div>
                
                <form method="POST" enctype="multipart/form-data">
                    <div class="field">
                        <label class="label">帳戶名稱 *</label>
                        <div class="control">
                            <input class="input" type="text" name="name" placeholder="請輸入現金帳戶名稱" required>
                        </div>
                    </div>

                    <div class="field is-horizontal">
                        <div class="field-label is-normal">
                            <label class="label">期初金額 <span class="icon has-text-info is-small"><i class="fas fa-info-circle"></i></span></label>
                        </div>
                        <div class="field-body">
                            <div class="field">
                                <div class="control">
                                    <input class="input" id="init_amount" name="init_amount" type="text" value="1,000">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">帳戶類別</label>
                        <div class="control">
                            <div class="select is-fullwidth">
                                <select name="account_type">
                                    <option value="現金" selected>現金</option>
                                    <option value="銀行存款">銀行存款</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">科目代碼</label>
                        <div class="control">
                            <input class="input" type="text" name="subject_code" value="001">
                        </div>
                    </div>
                    
                    <div class="field">
                        <div class="control">
                            <label class="checkbox">
                                <input type="checkbox" name="is_default"> 預設帳戶
                            </label>
                        </div>
                    </div>
                    
                    <div class="field is-grouped is-grouped-centered mt-5">
                        <div class="control">
                            <button type="submit" class="button is-link is-medium">儲存修改</button>
                        </div>
                        <div class="control">
                            <button type="button" class="button is-danger is-medium">刪除</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script>
function toggleAmountEdit() {
  const input = document.getElementById('init_amount');
  const btn = document.getElementById('editAmountBtn');
  if (input.readOnly) {
    input.readOnly = false;
    input.classList.remove('is-static');
    btn.textContent = '完成';
    input.focus();
  } else {
    input.readOnly = true;
    btn.textContent = '修改';
  }
}
function showFileName(input) {
  document.getElementById('file-name').textContent = input.files[0]?.name || '';
}
</script>
</body>
</html> 