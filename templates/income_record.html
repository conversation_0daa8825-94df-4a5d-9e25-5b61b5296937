<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收入紀錄</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
        }

        .column.is-narrow {
            flex: none;
            width: 200px !important;
            max-width: 200px !important;
            min-width: 200px !important;
        }

        .sidebar {
            max-width: 200px;
            min-width: 180px;
            width: 200px;
        }

        .sidebar .menu-label {
            font-size: 14px;
            font-weight: bold;
        }

        .sidebar .menu-list a {
            font-size: 20px;
            padding: 0.5em 0.75em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .tab-custom {
            background: #2563eb;
            color: #fff;
            border-radius: 8px 8px 0 0;
            padding: 0.75rem 2rem;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .tab-custom.inactive {
            background: #e5e7eb;
            color: #222;
        }

        .box-shadow {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .is-toggle .button.is-selected {
            background: #2563eb;
            color: #fff;
        }

        .button.is-selected,
        .button.is-selected.is-primary {
            background: #2563eb !important;
            color: #fff !important;
            border-color: #2563eb !important;
            box-shadow: 0 2px 8px rgba(37, 99, 235, 0.08);
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=收支帳簿"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        收入紀錄
                    </h1>
                </div>
                <div class="box box-shadow" style="padding:0;">
                    <form class="p-5" method="post" enctype="multipart/form-data">
                        <div class="columns">
                            <div class="column is-6">
                                <div class="columns is-mobile">
                                    <div class="column is-3">
                                        <div class="field">
                                            <label class="label">記帳時間</label>
                                            <div class="control has-icons-right">
                                                <input class="input" type="date" name="a_time" value="2025-07-01">
                                                <span class="icon is-small is-right">
                                                    <i class="fas fa-calendar"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-4">
                                        <div class="field">
                                            <label class="label">名稱</label>
                                            <div class="control">
                                                <input class="input" type="text" name="name" placeholder="請輸入名稱">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-3">
                                        <div class="field">
                                            <label class="label">總計(含稅)</label>
                                            <div class="control">
                                                <input class="input" type="number" name="total" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-2">
                                        <div class="field">
                                            <label class="label">手續費</label>
                                            <div class="control">
                                                <input class="input" type="number" name="extra_fee" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="columns is-mobile">
                                    <div class="column">
                                        <div class="field">
                                            <label class="label">科目</label>
                                            <div class="control is-flex is-align-items-center">
                                                <div class="select">
                                                    <select id="subject-group" name="subject_group">
                                                        <option value="">請選擇科目大類</option>
                                                    </select>
                                                </div>
                                                <div class="select ml-2">
                                                    <select id="subject-code" name="subject_code" disabled>
                                                        <option value="">請選擇科目代碼</option>
                                                    </select>
                                                </div>
                                                <button type="button" class="button is-link ml-3" id="edit-subject-btn"
                                                    title="管理科目" style="padding: 0.4rem 0.7rem;">
                                                    <span class="icon is-medium">
                                                        <i class="fas fa-pen"
                                                            style="color: #fff; font-size: 1.3rem;"></i>
                                                    </span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column">
                                        <div class="field">
                                            <label class="label">資金帳戶</label>
                                            <div class="control">
                                                <div class="select is-fullwidth">
                                                    <select name="account_id">
                                                        <option value="">請選擇帳戶</option>
                                                        {% for acc in accounts %}
                                                        <option value="{{ acc.id }}">{{ acc.name }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label class="label">收支對象</label>
                                    <div class="control is-flex is-align-items-center">
                                        <div class="select">
                                            <select id="identity-type" name="identity_type">
                                                <option value="">請選擇類型</option>
                                                {% for type_name in identities_by_type.keys() %}
                                                <option value="{{ type_name }}">{{ type_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="select ml-2">
                                            <select id="identity-name" name="payment_identity_id" disabled>
                                                <option value="">請選擇名稱</option>
                                            </select>
                                        </div>
                                        <button type="button" class="button is-link ml-3" id="manage-identity-btn"
                                            title="管理收支對象" style="padding: 0.4rem 0.7rem;">
                                            <span class="icon is-medium">
                                                <i class="fas fa-users" style="color: #fff; font-size: 1.3rem;"></i>
                                            </span>
                                        </button>
                                    </div>
                                </div>
                                <!-- 使用更多功能模組 -->
                                {% include 'more_section.html' %}
                            </div>
                            <div class="column is-6">
                                <!-- 使用憑證模組 -->
                                {% include 'voucher_section.html' %}

                                <!-- 使用收付款狀態模組 -->
                                {% include 'payment_status_section.html' %}
                            </div>
                        </div>
                        <div class="field is-grouped is-justify-content-flex-end mt-5">
                            <div class="control">
                                <button class="button is-primary is-large" type="submit">儲存</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const subjects = [
                {
                    group: "營業收入",
                    items: [
                        { name: "銷貨收入", code: "4100" },
                        { name: "營業收入", code: "4101" },
                        { name: "銷貨退回", code: "4110" },
                        { name: "銷貨折讓", code: "4120" },
                        { name: "工程收入", code: "4103" }
                    ]
                },
                {
                    group: "其他收入",
                    items: [
                        { name: "預收收入/遞延", code: "2167" },
                        { name: "服務收入", code: "4102" },
                        { name: "利息收入", code: "7040" },
                        { name: "租金收入", code: "7050" },
                        { name: "處分資產收益", code: "7060" },
                        { name: "處分投資收益", code: "7065" },
                        { name: "兌換盈益", code: "7090" },
                        { name: "其他收入", code: "7100" },
                    ]
                }
            ];

            const groupSelect = document.getElementById('subject-group');
            const codeSelect = document.getElementById('subject-code');

            subjects.forEach((g, idx) => {
                const opt = document.createElement('option');
                opt.value = idx;
                opt.textContent = g.group;
                groupSelect.appendChild(opt);
            });

            groupSelect.addEventListener('change', function () {
                codeSelect.innerHTML = '<option value="">請選擇科目代碼</option>';
                if (this.value === '') {
                    codeSelect.disabled = true;
                    return;
                }
                const items = subjects[this.value].items;
                items.forEach((item) => {
                    const opt = document.createElement('option');
                    opt.value = item.code;
                    opt.textContent = `${item.code} ${item.name}`;
                    codeSelect.appendChild(opt);
                });
                codeSelect.disabled = false;
            });

            document.getElementById('edit-subject-btn').addEventListener('click', function () {
                window.open('/accounting/subject_manage', '_blank', 'width=900,height=700');
            });

            // 收支對象兩層選單邏輯
            const identitiesByType = JSON.parse('{{ identities_by_type | tojson | safe }}');
            const identityTypeSelect = document.getElementById('identity-type');
            const identityNameSelect = document.getElementById('identity-name');

            identityTypeSelect.addEventListener('change', function () {
                identityNameSelect.innerHTML = '<option value="">請選擇名稱</option>';
                if (this.value === '') {
                    identityNameSelect.disabled = true;
                    return;
                }

                const identities = identitiesByType[this.value] || [];
                identities.forEach(function (identity) {
                    const opt = document.createElement('option');
                    opt.value = identity.id;
                    opt.textContent = identity.name;
                    identityNameSelect.appendChild(opt);
                });
                identityNameSelect.disabled = false;
            });

            document.getElementById('manage-identity-btn').addEventListener('click', function () {
                window.open('/payment_identity_list', '_blank', 'width=1000,height=700');
            });

            // 憑證、收付款狀態、更多功能、發票號碼檢查的 JavaScript 已移至各自的模組中
        });
    </script>
</body>

</html>