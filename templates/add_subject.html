<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>新增會計子科目</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        .main-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 2.5rem 2rem;
            max-width: 900px;
            margin: 2rem auto;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 1.2rem;
        }

        .field-label {
            font-weight: bold;
            color: #222;
        }

        .is-readonly {
            background: #f5f6fa;
            color: #888;
        }

        .form-divider {
            border-left: 1.5px dashed #bbb;
            height: 100%;
            margin: 0 1.5rem;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="main-card">
                    <div class="section-title mb-5">
                        <a href="javascript:history.back()"
                            style="color:#2563eb;text-decoration:none;font-size:1.1rem;">←</a>
                        新增會計子科目
                    </div>
                    <form method="post">
                        <div class="columns">
                            <!-- 左欄：母科目資訊 -->
                            <div class="column is-5">
                                <div class="box" style="background:#f5f6fa;">
                                    <div class="field mb-3">
                                        <label class="label">母科目名稱</label>
                                        <div class="control">
                                            <input class="input is-readonly" type="text"
                                                value="{{ parent_info.code if parent_info }} {{ parent_info.name if parent_info }}"
                                                readonly>
                                            <input type="hidden" name="parent_code"
                                                value="{{ parent_info.code if parent_info }}">
                                        </div>
                                    </div>
                                    <div class="field mb-3">
                                        <label class="label">母科目類型</label>
                                        <div class="control">
                                            <input class="input is-readonly" type="text"
                                                value="{{ parent_info.top_category if parent_info }}" readonly>
                                        </div>
                                    </div>
                                    <div class="field mb-3">
                                        <label class="label">是否可扣抵</label>
                                        <div class="control">
                                            <input class="input is-readonly" type="text"
                                                value="{{ '可扣抵' if parent_info and parent_info.is_expandable else '不適用' }}"
                                                readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-divider"></div>
                            <!-- 右欄：新增子科目表單 -->
                            <div class="column is-6">
                                <div class="box">
                                    <div class="field mb-3">
                                        <label class="label">子科目名稱</label>
                                        <div class="control">
                                            <input class="input" name="sub_name" type="text" placeholder="請輸入子科目名稱"
                                                required>
                                        </div>
                                    </div>
                                    <div class="field mb-3">
                                        <label class="label">子科目代碼</label>
                                        <div class="control">
                                            <input class="input" name="sub_code" type="text" placeholder="請輸入子科目代碼"
                                                required value="{{ next_sub_code }}">
                                        </div>
                                    </div>
                                    <div class="field mb-3">
                                        <label class="label">備註</label>
                                        <div class="control">
                                            <textarea class="textarea" name="sub_note" placeholder="備註"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="field is-grouped is-grouped-right mt-4">
                            <div class="control">
                                <a class="button is-light" href="javascript:history.back()">取消</a>
                            </div>
                            <div class="control">
                                <button class="button is-link" type="submit">儲存</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>

</html>