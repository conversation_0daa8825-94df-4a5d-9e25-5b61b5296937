# 📁 項目結構說明

## 🏗️ 目錄結構

```
accounting/
├── app.py                 # 🚀 主應用入口
├── model.py              # 📊 資料模型
├── database.py           # 💾 資料庫配置
├── 
├── routes/               # 🛣️ 路由模組
│   ├── main.py
│   ├── accounting.py
│   ├── account.py
│   └── ...
├── 
├── templates/            # 🎨 HTML 模板
├── static/              # 📁 靜態資源
│   └── uploads/         # 📤 上傳文件
├── 
├── tests/               # 🧪 測試文件
├── docs/                # 📚 文檔
├── scripts/             # 📜 工具腳本
├── data/                # 💾 資料文件
├── config/              # ⚙️ 配置文件
├── backups/             # 📦 備份文件
├── logs/                # 📝 日誌文件
├── 
├── utils/               # 🔧 工具函數
├── services/            # 🏢 業務服務
├── alembic/             # 🔄 資料庫遷移
└── .venv/               # 🐍 虛擬環境
```

## 🚀 啟動方式

```bash
python app.py
```

## 🧪 運行測試

```bash
python scripts/run_tests.py
```

## 📚 查看文檔

```bash
cat docs/README.md
```

---
*此文件由項目整理腳本自動生成*
