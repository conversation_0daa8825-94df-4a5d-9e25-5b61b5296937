from flask import Blueprint, render_template, request, redirect, url_for
from sqlalchemy.orm import sessionmaker
from data.menu_data import menu
from model import Account, PaymentIdentity, Money, CompanyInfo, Department, Project, engine
from datetime import datetime
import os

Session = sessionmaker(bind=engine)

income_expense_bp = Blueprint('income_expense', __name__)

@income_expense_bp.route('/income_record', methods=['GET', 'POST'])
def income_record():
    """收入紀錄頁面"""
    db=Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    accounts = db.query(Account).all()
    # 取得部門資料
    departments = db.query(Department).all()
    # 取得專案資料
    projects = db.query(Project).all()
    
    # 取得收支對象資料，按類型分組
    payment_identities = db.query(PaymentIdentity).all()
    identities_by_type = {}
    for identity in payment_identities:
        if identity.type not in identities_by_type:
            identities_by_type[identity.type] = []
        identities_by_type[identity.type].append({
            'id': identity.id,
            'name': identity.name,
            'tax_id': identity.tax_id
        })
    # 查詢公司統編
    company = db.query(CompanyInfo).first()
    company_id = company.company_id if company else ''

    def parse_date(val):
        if val:
            try:
                return datetime.strptime(val, '%Y-%m-%d')
            except Exception:
                return None
        return None

    if request.method == 'POST':
        print('收到POST', request.form)
        form = request.form
        invoice_number = form.get('invoice_number')
        paper_status = form.get('paper_status')  # 憑證狀態 radio
        is_paper = form.get('is_paper')         # 收據類憑證 checkbox
        # 只有「有憑證」且「收據類憑證」沒打勾時才檢查
        if paper_status == 'has' and not is_paper and invoice_number:
            exists = db.query(Money).filter(Money.number == invoice_number).first()
            if exists:
                db.close()
                return render_template(
                    'income_record.html',
                    sidebar_items=main_menu,
                    selected=selected,
                    accounts=accounts,
                    departments=departments,
                    projects=projects,
                    identities_by_type=identities_by_type,
                    company_id=company_id,
                    error='發票號碼已存在，請重新輸入'
                )
        money = Money(
            a_time = parse_date(form.get('a_time')),
            name = form.get('name'),
            total = form.get('total') or 0,
            extra_fee = form.get('extra_fee') or 0,
            subject_code = form.get('subject_code'),
            account_id = form.get('account_id'),
            payment_identity_id = form.get('payment_identity_id'),
            money_type = '收入',
            is_paper = bool(form.get('is_paper')),
            number = invoice_number,
            tax_type = form.get('tax_type'),
            buyer_tax_id = form.get('buyer_tax_id'),
            seller_tax_id = form.get('seller_tax_id'),
            date = form.get('invoice_date'),
            is_paid = form.get('is_paid') == '1',
            should_paid_date = parse_date(form.get('should_paid_date')),
            paid_date = parse_date(form.get('paid_date')),
            note = form.get('note'),
            department_id = form.get('department_id'),
            project_id = form.get('project_id'),
            tags = form.get('tag'),
            image_path = None
        )
        db.add(money)
        db.commit()
        # 檔案上傳處理
        file = request.files.get('other_file')
        print('收到檔案:', file, file.filename if file else None)
        if file and file.filename:
            upload_dir = os.path.join(os.path.dirname(__file__), '../static/uploads')
            os.makedirs(upload_dir, exist_ok=True)
            today_str = datetime.now().strftime('%Y%m%d')
            ext = os.path.splitext(file.filename)[1]
            filename = f"{today_str}_{money.id}{ext}"
            save_path = os.path.join(upload_dir, filename)
            file.save(save_path)
            # 更新 image_path
            money.image_path = f"/static/uploads/{filename}"
            print('更新 image_path:', money.image_path)
            db.commit()
        return redirect(url_for('income_expense.income_record'))

    return render_template('income_record.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        accounts=accounts,
                        departments=departments,
                        projects=projects,
                        identities_by_type=identities_by_type,
                        company_id=company_id)

@income_expense_bp.route('/expense_record', methods=['GET', 'POST'])
def expense_record():
    """支出紀錄頁面"""
    db=Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    accounts = db.query(Account).all()
    
    # 取得部門資料
    departments = db.query(Department).all()
    
    # 取得專案資料
    projects = db.query(Project).all()
    
    # 取得收支對象資料，按類型分組
    payment_identities = db.query(PaymentIdentity).all()
    identities_by_type = {}
    for identity in payment_identities:
        if identity.type not in identities_by_type:
            identities_by_type[identity.type] = []
        identities_by_type[identity.type].append({
            'id': identity.id,
            'name': identity.name,
            'tax_id': identity.tax_id
        })
    # 查詢公司統編
    company = db.query(CompanyInfo).first()
    company_id = company.company_id if company else ''

    def parse_date(val):
        if val:
            try:
                return datetime.strptime(val, '%Y-%m-%d')
            except Exception:
                return None
        return None

    if request.method == 'POST':
        print('收到POST', request.form)
        form = request.form
        invoice_number = form.get('invoice_number')
        paper_status = form.get('paper_status')  # 憑證狀態 radio
        is_paper = form.get('is_paper')         # 收據類憑證 checkbox
        # 只有「有憑證」且「收據類憑證」沒打勾時才檢查
        if paper_status == 'has' and not is_paper and invoice_number:
            exists = db.query(Money).filter(Money.number == invoice_number).first()
            if exists:
                db.close()
                return render_template(
                    'expense_record.html',
                    sidebar_items=main_menu,
                    selected=selected,
                    accounts=accounts,
                    departments=departments,
                    projects=projects,
                    identities_by_type=identities_by_type,
                    company_id=company_id,
                    error='發票號碼已存在，請重新輸入'
                )
        money = Money(
            a_time = parse_date(form.get('a_time')),
            name = form.get('name'),
            total = form.get('total') or 0,
            extra_fee = form.get('extra_fee') or 0,
            subject_code = form.get('subject_code'),
            account_id = form.get('account_id'),
            payment_identity_id = form.get('payment_identity_id'),
            money_type = '支出',
            is_paper = bool(form.get('is_paper')),
            number = invoice_number,
            tax_type = form.get('tax_type'),
            buyer_tax_id = form.get('buyer_tax_id'),
            seller_tax_id = form.get('seller_tax_id'),
            date = form.get('invoice_date'),
            is_paid = form.get('is_paid') == '1',
            should_paid_date = parse_date(form.get('should_paid_date')),
            paid_date = parse_date(form.get('paid_date')),
            note = form.get('note'),
            department_id = form.get('department_id'),
            project_id = form.get('project_id'),
            tags = form.get('tag'),
            image_path = None
        )
        db.add(money)
        db.commit()
        # 檔案上傳處理
        file = request.files.get('other_file')
        print('收到檔案:', file, file.filename if file else None)
        if file and file.filename:
            upload_dir = os.path.join(os.path.dirname(__file__), '../static/uploads')
            os.makedirs(upload_dir, exist_ok=True)
            today_str = datetime.now().strftime('%Y%m%d')
            ext = os.path.splitext(file.filename)[1]
            filename = f"{today_str}_{money.id}{ext}"
            save_path = os.path.join(upload_dir, filename)
            file.save(save_path)
            # 更新 image_path
            money.image_path = f"/static/uploads/{filename}"
            db.commit()
        return redirect(url_for('income_expense.expense_record'))

    return render_template('expense_record.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        accounts=accounts,
                        departments=departments,
                        projects=projects,
                        identities_by_type=identities_by_type,
                        company_id=company_id)

@income_expense_bp.route('/income_list')
def income_list():
    """收入列表頁面"""
    db = Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    
    # 取得查詢參數
    date_type = request.args.get('date_type', 'a_time')
    date_start = request.args.get('date_start')
    date_end = request.args.get('date_end')
    hide_paid = request.args.get('hide_paid')

    # 構建查詢條件
    query = db.query(Money).filter(Money.money_type == '收入')
    if date_start and date_end and date_type in ['a_time', 'should_paid_date', 'paid_date', 'invoice_date']:
        if date_type == 'invoice_date':
            query = query.filter(Money.date >= date_start, Money.date <= date_end)
        else:
            query = query.filter(getattr(Money, date_type) >= date_start, getattr(Money, date_type) <= date_end)
    if hide_paid:
        query = query.filter(not Money.is_paid)
    income_records = query.order_by(Money.a_time.desc()).all()
    
    # 準備顯示資料
    income_data = []
    for record in income_records:
        subject_name = record.subject.name if record.subject else ''
        account_name = record.account.name if record.account else ''
        payment_identity_name = record.payment_identity.name if record.payment_identity else ''
        a_time_str = record.a_time.strftime('%Y-%m-%d') if record.a_time else ''
        should_paid_date_str = record.should_paid_date.strftime('%Y-%m-%d') if record.should_paid_date else ''
        paid_date_str = record.paid_date.strftime('%Y-%m-%d') if record.paid_date else ''
        invoice_date_str = record.date if record.date else ''
        total_str = f"{record.total:,}" if record.total else '0'
        payment_status = '已收付款' if record.is_paid else '未收付款'
        income_data.append({
            'id': record.id,
            'a_time': a_time_str,
            'total': total_str,
            'subject_name': subject_name,
            'name': record.name or '',
            'payment_identity_name': payment_identity_name,
            'account_name': account_name,
            'payment_status': payment_status,
            'should_paid_date': should_paid_date_str,
            'paid_date': paid_date_str,
            'invoice_date': invoice_date_str,
            'note': record.note or ''
        })
    db.close()
    return render_template('income_list.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        income_records=income_data)

@income_expense_bp.route('/expense_list')
def expense_list():
    """支出列表頁面"""
    db = Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    
    # 取得查詢參數
    date_type = request.args.get('date_type', 'a_time')
    date_start = request.args.get('date_start')
    date_end = request.args.get('date_end')
    hide_paid = request.args.get('hide_paid')

    # 構建查詢條件
    query = db.query(Money).filter(Money.money_type == '支出')
    if date_start and date_end and date_type in ['a_time', 'should_paid_date', 'paid_date', 'invoice_date']:
        if date_type == 'invoice_date':
            query = query.filter(Money.date >= date_start, Money.date <= date_end)
        else:
            query = query.filter(getattr(Money, date_type) >= date_start, getattr(Money, date_type) <= date_end)
    if hide_paid:
        query = query.filter(not Money.is_paid)
    expense_records = query.order_by(Money.a_time.desc()).all()
    
    # 準備顯示資料
    expense_data = []
    for record in expense_records:
        subject_name = record.subject.name if record.subject else ''
        account_name = record.account.name if record.account else ''
        payment_identity_name = record.payment_identity.name if record.payment_identity else ''
        a_time_str = record.a_time.strftime('%Y-%m-%d') if record.a_time else ''
        should_paid_date_str = record.should_paid_date.strftime('%Y-%m-%d') if record.should_paid_date else ''
        paid_date_str = record.paid_date.strftime('%Y-%m-%d') if record.paid_date else ''
        invoice_date_str = record.date if record.date else ''
        total_str = f"{record.total:,}" if record.total else '0'
        payment_status = '已收付款' if record.is_paid else '未收付款'
        expense_data.append({
            'id': record.id,
            'a_time': a_time_str,
            'total': total_str,
            'subject_name': subject_name,
            'name': record.name or '',
            'payment_identity_name': payment_identity_name,
            'account_name': account_name,
            'payment_status': payment_status,
            'should_paid_date': should_paid_date_str,
            'paid_date': paid_date_str,
            'invoice_date': invoice_date_str,
            'note': record.note or ''
        })
    db.close()
    return render_template('expense_list.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        expense_records=expense_data)

@income_expense_bp.route('/ac_delay_list')
def ac_delay_list():
    """應收應付逾期列表"""
    db = Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    from datetime import date
    today = date.today()
    # 查詢逾期記錄
    overdue_records = db.query(Money).filter(
        Money.should_paid_date < today,
        Money.is_paid == False
    ).order_by(Money.should_paid_date.asc()).all()
    
    # 準備顯示資料
    overdue_data = []
    for record in overdue_records:
        subject_name = record.subject.name if record.subject else ''
        account_name = record.account.name if record.account else ''
        payment_identity_name = record.payment_identity.name if record.payment_identity else ''
        a_time_str = record.a_time.strftime('%Y-%m-%d') if record.a_time else ''
        should_paid_date_str = record.should_paid_date.strftime('%Y-%m-%d') if record.should_paid_date else ''
        paid_date_str = record.paid_date.strftime('%Y-%m-%d') if record.paid_date else ''
        invoice_date_str = record.date if record.date else ''
        total_str = f"{record.total:,}" if record.total else '0'
        payment_status = '已收付款' if record.is_paid else '未收付款'
        
        # 計算逾期天數
        overdue_days = (today - record.should_paid_date.date()).days if record.should_paid_date else 0
        
        overdue_data.append({
            'id': record.id,
            'a_time': a_time_str,
            'total': total_str,
            'subject_name': subject_name,
            'name': record.name or '',
            'payment_identity_name': payment_identity_name,
            'account_name': account_name,
            'payment_status': payment_status,
            'should_paid_date': should_paid_date_str,
            'paid_date': paid_date_str,
            'invoice_date': invoice_date_str,
            'note': record.note or '',
            'money_type': record.money_type,
            'overdue_days': overdue_days
        })
    
    db.close()
    return render_template('ac_delay_list.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        overdue_records=overdue_data) 