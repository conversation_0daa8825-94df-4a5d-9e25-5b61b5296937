from flask import Blueprint, render_template, request
from model import Account, AccountSubject
from database import get_db
from data.menu_data import menu
from model import PaymentIdentity

assets_bp = Blueprint('assets', __name__)

@assets_bp.route('/add_prepaid_expense', methods=['GET', 'POST'])
def add_prepaid_expense():
    with get_db() as db:
        subjects = db.query(AccountSubject).filter(
            AccountSubject.code.in_(['6020', '6030', '6050', '6080', '6060', '6230'])
        ).all()
        accounts = db.query(Account).all()
        identities = db.query(PaymentIdentity).all()
        subject_list = [{'id': s.id, 'name': s.name, 'code': s.code} for s in subjects]
        account_list = [{'id': a.id, 'name': a.name} for a in accounts]
        identity_list = [{'id': i.id, 'name': i.name} for i in identities]
    sidebar_items = list(menu.keys())
    selected = '資產管理'
    return render_template(
        'add_prepaid_expense.html',
        subjects=subject_list,
        accounts=account_list,
        identities=identity_list,
        sidebar_items=sidebar_items,
        selected=selected
    )

@assets_bp.route('/add_amortization', methods=['GET', 'POST'])
def add_amortization():
    """新增各項攤提"""
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    
    main_menu = list(menu.keys())
    selected = '資產管理'
    return render_template('add_amortization.html', 
                         sidebar_items=main_menu, 
                         selected=selected)

@assets_bp.route('/add_fixed_asset', methods=['GET', 'POST'])
def add_fixed_asset():
    """新增固定資產"""
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    
    main_menu = list(menu.keys())
    selected = '資產管理'
    return render_template('add_fixed_asset.html', 
                         sidebar_items=main_menu, 
                         selected=selected)

@assets_bp.route('/add_intangible_asset', methods=['GET', 'POST'])
def add_intangible_asset():
    """新增無形資產"""
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    
    main_menu = list(menu.keys())
    selected = '資產管理'
    return render_template('add_intangible_asset.html', 
                         sidebar_items=main_menu, 
                         selected=selected)

@assets_bp.route('/asset_list', methods=['GET'])
def asset_list():
    """財產列表"""
    main_menu = list(menu.keys())
    selected = '資產管理'
    return render_template('asset_list.html', 
                         sidebar_items=main_menu, 
                         selected=selected) 