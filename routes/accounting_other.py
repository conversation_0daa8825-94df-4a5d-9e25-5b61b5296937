from flask import Blueprint, render_template
from data.menu_data import menu

accounting_other_bp = Blueprint('accounting_other', __name__)

@accounting_other_bp.route('/voucher_manage')
def voucher_manage():
    """傳票管理"""
    main_menu = list(menu.keys())
    selected = '會計科目'
    return render_template('voucher_manage.html', 
                         sidebar_items=main_menu, 
                         selected=selected)

@accounting_other_bp.route('/cost_transfer')
def cost_transfer():
    """成本結轉"""
    main_menu = list(menu.keys())
    selected = '會計科目'
    return render_template('cost_transfer.html', 
                         sidebar_items=main_menu, 
                         selected=selected)

@accounting_other_bp.route('/tax_manage')
def tax_manage():
    """進/銷項稅額管理"""
    main_menu = list(menu.keys())
    selected = '會計科目'
    return render_template('tax_manage.html', 
                         sidebar_items=main_menu, 
                         selected=selected) 