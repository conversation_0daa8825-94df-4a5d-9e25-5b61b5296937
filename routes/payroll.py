from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from data.menu_data import menu
from model import Employee, session, CompanyInfo
from datetime import datetime
from sqlalchemy.exc import IntegrityError

payroll_bp = Blueprint('payroll', __name__)

@payroll_bp.route('/payroll_process', methods=['GET', 'POST'])
def payroll_process():
    """發薪作業"""
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    
    main_menu = list(menu.keys())
    selected = '薪資報酬'
    return render_template('payroll_process.html', 
                         sidebar_items=main_menu, 
                         selected=selected)

@payroll_bp.route('/insurance_manage', methods=['GET', 'POST'])
def insurance_manage():
    """勞保/健保/退休金管理"""
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    
    main_menu = list(menu.keys())
    selected = '薪資報酬'
    return render_template('insurance_manage.html', 
                         sidebar_items=main_menu, 
                         selected=selected)

@payroll_bp.route('/add_employee', methods=['GET', 'POST'])
def add_employee():
    """新增員工"""
    if request.method == 'POST':
        try:
            # 處理勞保投保項目（多選）
            labor_insurance_items = request.form.getlist('labor_insurance_item')
            labor_insurance_items_str = ','.join(labor_insurance_items) if labor_insurance_items else ''

            # 建立新員工
            employee = Employee(
                # 基本資料
                title=request.form.get('title'),
                emp_id=request.form.get('emp_id'),
                name=request.form.get('name'),
                identity=request.form.get('identity'),
                onboard_date=datetime.strptime(request.form.get('onboard_date'), '%Y-%m-%d').date() if request.form.get('onboard_date') else None,
                leave_date=datetime.strptime(request.form.get('leave_date'), '%Y-%m-%d').date() if request.form.get('leave_date') else None,
                department_name=request.form.get('department'),
                address=request.form.get('address'),
                phone=request.form.get('phone'),
                email=request.form.get('email'),

                # 薪資資訊
                salary=int(request.form.get('salary', 0)),
                meal=int(request.form.get('meal', 0)),
                bank=request.form.get('bank'),
                bank_account=request.form.get('bank_account'),

                # 保險身份
                insurance_identity=request.form.get('insurance_identity'),
                labor_insurance=request.form.get('labor_insurance'),
                health_insurance=request.form.get('health_insurance'),

                # 健康保險相關
                health_insurance_date=datetime.strptime(request.form.get('health_insurance_date'), '%Y-%m-%d').date() if request.form.get('health_insurance_date') else None,
                health_subsidy_qualification=request.form.get('health_subsidy_qualification'),
                health_law_effective_date=datetime.strptime(request.form.get('health_law_effective_date'), '%Y-%m-%d').date() if request.form.get('health_law_effective_date') else None,
                health_level=request.form.get('health_level'),

                # 健保眷屬
                dependents_none=int(request.form.get('dependents_none', 0)),
                dependents_1_4=int(request.form.get('dependents_1_4', 0)),
                dependents_1_2=int(request.form.get('dependents_1_2', 0)),
                dependents_local=int(request.form.get('dependents_local', 0)),
                dependents_full=int(request.form.get('dependents_full', 0)),

                # 勞工保險相關
                labor_insurance_date=datetime.strptime(request.form.get('labor_insurance_date'), '%Y-%m-%d').date() if request.form.get('labor_insurance_date') else None,
                labor_law_effective_date=datetime.strptime(request.form.get('labor_law_effective_date'), '%Y-%m-%d').date() if request.form.get('labor_law_effective_date') else None,
                labor_level=request.form.get('labor_level'),
                labor_insurance_items=labor_insurance_items_str,

                # 職業災害保險相關
                occupational_law_effective_date=datetime.strptime(request.form.get('occupational_law_effective_date'), '%Y-%m-%d').date() if request.form.get('occupational_law_effective_date') else None,
                occupational_level=request.form.get('occupational_level')
            )

            session.add(employee)
            session.commit()
            flash('員工資料新增成功！', 'success')
            return redirect(url_for('payroll.employee_list'))

        except IntegrityError as e:
            session.rollback()
            if 'emp_id' in str(e):
                flash('員工編號已存在，請使用不同的編號！', 'error')
            elif 'identity' in str(e):
                flash('身份證號已存在，請檢查輸入！', 'error')
            else:
                flash('資料新增失敗，請檢查輸入資料！', 'error')
        except ValueError as e:
            session.rollback()
            flash('日期格式錯誤，請檢查輸入！', 'error')
        except Exception as e:
            session.rollback()
            flash(f'新增失敗：{str(e)}', 'error')

    # 獲取公司統編（用於賣方統編預設值）
    company_info = session.query(CompanyInfo).first()
    company_id = company_info.company_id if company_info else ''

    main_menu = list(menu.keys())
    selected = '薪資報酬'
    return render_template('add_employee.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         company_id=company_id)

@payroll_bp.route('/employee_list', methods=['GET'])
def employee_list():
    """員工管理列表"""
    # 獲取查詢參數
    search = request.args.get('search', '')
    page = int(request.args.get('page', 1))
    per_page = 20

    # 建立查詢
    query = session.query(Employee).filter(Employee.is_active == True)

    # 搜尋功能
    if search:
        query = query.filter(
            (Employee.name.contains(search)) |
            (Employee.emp_id.contains(search)) |
            (Employee.identity.contains(search)) |
            (Employee.department_name.contains(search))
        )

    # 排序
    query = query.order_by(Employee.onboard_date.desc(), Employee.created_at.desc())

    # 分頁
    total = query.count()
    employees = query.offset((page - 1) * per_page).limit(per_page).all()

    # 計算分頁資訊
    total_pages = (total + per_page - 1) // per_page
    has_prev = page > 1
    has_next = page < total_pages

    main_menu = list(menu.keys())
    selected = '薪資報酬'
    return render_template('employee_list.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         employees=employees,
                         search=search,
                         page=page,
                         total_pages=total_pages,
                         has_prev=has_prev,
                         has_next=has_next,
                         total=total)

@payroll_bp.route('/edit_employee/<int:employee_id>', methods=['GET', 'POST'])
def edit_employee(employee_id):
    """編輯員工資料"""
    employee = session.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        flash('員工不存在！', 'error')
        return redirect(url_for('payroll.employee_list'))

    if request.method == 'POST':
        try:
            # 處理勞保投保項目（多選）
            labor_insurance_items = request.form.getlist('labor_insurance_item')
            labor_insurance_items_str = ','.join(labor_insurance_items) if labor_insurance_items else ''

            # 更新員工資料
            employee.title = request.form.get('title')
            employee.emp_id = request.form.get('emp_id')
            employee.name = request.form.get('name')
            employee.identity = request.form.get('identity')
            employee.onboard_date = datetime.strptime(request.form.get('onboard_date'), '%Y-%m-%d').date() if request.form.get('onboard_date') else None
            employee.leave_date = datetime.strptime(request.form.get('leave_date'), '%Y-%m-%d').date() if request.form.get('leave_date') else None
            employee.department_name = request.form.get('department')
            employee.address = request.form.get('address')
            employee.phone = request.form.get('phone')
            employee.email = request.form.get('email')

            # 薪資資訊
            employee.salary = int(request.form.get('salary', 0))
            employee.meal = int(request.form.get('meal', 0))
            employee.bank = request.form.get('bank')
            employee.bank_account = request.form.get('bank_account')

            # 保險身份
            employee.insurance_identity = request.form.get('insurance_identity')
            employee.labor_insurance = request.form.get('labor_insurance')
            employee.health_insurance = request.form.get('health_insurance')

            # 健康保險相關
            employee.health_insurance_date = datetime.strptime(request.form.get('health_insurance_date'), '%Y-%m-%d').date() if request.form.get('health_insurance_date') else None
            employee.health_subsidy_qualification = request.form.get('health_subsidy_qualification')
            employee.health_law_effective_date = datetime.strptime(request.form.get('health_law_effective_date'), '%Y-%m-%d').date() if request.form.get('health_law_effective_date') else None
            employee.health_level = request.form.get('health_level')

            # 健保眷屬
            employee.dependents_none = int(request.form.get('dependents_none', 0))
            employee.dependents_1_4 = int(request.form.get('dependents_1_4', 0))
            employee.dependents_1_2 = int(request.form.get('dependents_1_2', 0))
            employee.dependents_local = int(request.form.get('dependents_local', 0))
            employee.dependents_full = int(request.form.get('dependents_full', 0))

            # 勞工保險相關
            employee.labor_insurance_date = datetime.strptime(request.form.get('labor_insurance_date'), '%Y-%m-%d').date() if request.form.get('labor_insurance_date') else None
            employee.labor_law_effective_date = datetime.strptime(request.form.get('labor_law_effective_date'), '%Y-%m-%d').date() if request.form.get('labor_law_effective_date') else None
            employee.labor_level = request.form.get('labor_level')
            employee.labor_insurance_items = labor_insurance_items_str

            # 職業災害保險相關
            employee.occupational_law_effective_date = datetime.strptime(request.form.get('occupational_law_effective_date'), '%Y-%m-%d').date() if request.form.get('occupational_law_effective_date') else None
            employee.occupational_level = request.form.get('occupational_level')

            session.commit()
            flash('員工資料更新成功！', 'success')
            return redirect(url_for('payroll.employee_list'))

        except IntegrityError as e:
            session.rollback()
            if 'emp_id' in str(e):
                flash('員工編號已存在，請使用不同的編號！', 'error')
            elif 'identity' in str(e):
                flash('身份證號已存在，請檢查輸入！', 'error')
            else:
                flash('資料更新失敗，請檢查輸入資料！', 'error')
        except ValueError as e:
            session.rollback()
            flash('日期格式錯誤，請檢查輸入！', 'error')
        except Exception as e:
            session.rollback()
            flash(f'更新失敗：{str(e)}', 'error')

    # 獲取公司統編
    company_info = session.query(CompanyInfo).first()
    company_id = company_info.company_id if company_info else ''

    # 處理勞保投保項目顯示
    labor_insurance_items = employee.labor_insurance_items.split(',') if employee.labor_insurance_items else []

    main_menu = list(menu.keys())
    selected = '薪資報酬'
    return render_template('add_employee.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         employee=employee,
                         labor_insurance_items=labor_insurance_items,
                         company_id=company_id,
                         is_edit=True)

@payroll_bp.route('/delete_employee/<int:employee_id>', methods=['POST'])
def delete_employee(employee_id):
    """刪除員工（軟刪除）"""
    employee = session.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        flash('員工不存在！', 'error')
        return redirect(url_for('payroll.employee_list'))

    try:
        employee.is_active = False
        session.commit()
        flash('員工資料已刪除！', 'success')
    except Exception as e:
        session.rollback()
        flash(f'刪除失敗：{str(e)}', 'error')

    return redirect(url_for('payroll.employee_list'))