from flask import Blueprint, render_template, request, redirect, url_for, flash
from data.menu_data import menu
from database import get_db
from model import CompanyInfo, Account, PaymentIdentity, Department, Project, SalarySetting
from datetime import datetime

settings_bp = Blueprint('settings', __name__)

def parse_date(val):
    """將字串日期轉換為 datetime 物件"""
    if val:
        try:
            return datetime.strptime(val, '%Y-%m-%d')
        except Exception:
            return None
    return None

@settings_bp.route('/company_setting', methods=['GET'])
def company_setting():
    """公司設定"""
    main_menu = list(menu.keys())
    selected = '薪資報酬'  # 修正：公司設定在薪資報酬選單下
    return render_template('company_setting.html',
                         sidebar_items=main_menu,
                         selected=selected)

@settings_bp.route('/salary_setting', methods=['GET', 'POST'])
def salary_setting():
    """薪資設定"""
    main_menu = list(menu.keys())
    selected = '薪資報酬'  # 修正：薪資設定也在薪資報酬選單下

    with get_db() as db:
        # 獲取銀行帳戶列表
        bank_accounts = db.query(Account).filter_by(category='銀行帳戶').order_by(Account.is_default.desc(), Account.name.asc()).all()

        # 轉換為字典格式，方便模板使用
        accounts_data = []
        for account in bank_accounts:
            account_display = f"{account.name}"
            if account.bank_name:
                account_display += f" ({account.bank_name}"
                if account.account_number:
                    # 只顯示帳號後4位
                    masked_number = "*" * (len(account.account_number) - 4) + account.account_number[-4:] if len(account.account_number) > 4 else account.account_number
                    account_display += f" {masked_number}"
                account_display += ")"

            accounts_data.append({
                'id': account.id,
                'name': account.name,
                'display_name': account_display,
                'is_default': account.is_default
            })

        # 獲取現有的薪資設定
        current_setting = db.query(SalarySetting).first()

        if request.method == 'POST':
            try:
                # 處理表單提交
                payday = int(request.form.get('payday', 10))
                fund_account_id = int(request.form.get('fund_account_id')) if request.form.get('fund_account_id') else None
                days_type = request.form.get('days_type', 'calendar')

                # 驗證資料
                if payday < 1 or payday > 31:
                    flash('發薪日必須在1-31之間！', 'error')
                    return redirect(url_for('settings.salary_setting'))

                if not fund_account_id:
                    flash('請選擇資金帳戶！', 'error')
                    return redirect(url_for('settings.salary_setting'))

                # 檢查帳戶是否存在
                account = db.query(Account).filter_by(id=fund_account_id).first()
                if not account:
                    flash('選擇的帳戶不存在！', 'error')
                    return redirect(url_for('settings.salary_setting'))

                # 儲存或更新設定
                if current_setting:
                    current_setting.payday = payday
                    current_setting.fund_account_id = fund_account_id
                    current_setting.days_type = days_type
                    current_setting.updated_at = datetime.now()
                else:
                    new_setting = SalarySetting(
                        payday=payday,
                        fund_account_id=fund_account_id,
                        days_type=days_type
                    )
                    db.add(new_setting)

                flash('薪資設定已儲存！', 'success')
                return redirect(url_for('settings.salary_setting'))

            except ValueError:
                flash('輸入資料格式錯誤！', 'error')
                return redirect(url_for('settings.salary_setting'))
            except Exception as e:
                flash(f'儲存失敗：{str(e)}', 'error')
                return redirect(url_for('settings.salary_setting'))

    return render_template('salary_setting.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         bank_accounts=accounts_data,
                         current_setting=current_setting)

@settings_bp.route('/basic_info', methods=['GET', 'POST'])
def basic_info():
    """基本資料"""
    main_menu = list(menu.keys())
    selected = '設定'
    
    # 台灣稅徵機關列表
    tax_offices = [
        '財政部北區國稅局', '財政部北區國稅局臺北分局', '財政部北區國稅局板橋分局',
        '財政部北區國稅局桃園分局', '財政部北區國稅局新竹分局', '財政部北區國稅局基隆分局',
        '財政部北區國稅局宜蘭分局', '財政部北區國稅局花蓮分局', '財政部北區國稅局金門分局',
        '財政部北區國稅局馬祖分局', '財政部中區國稅局', '財政部中區國稅局臺中分局',
        '財政部中區國稅局豐原分局', '財政部中區國稅局大屯分局', '財政部中區國稅局沙鹿分局',
        '財政部中區國稅局彰化分局', '財政部中區國稅局員林分局', '財政部中區國稅局南投分局',
        '財政部中區國稅局埔里分局', '財政部中區國稅局竹山分局', '財政部中區國稅局雲林分局',
        '財政部中區國稅局虎尾分局', '財政部中區國稅局北港分局', '財政部南區國稅局',
        '財政部南區國稅局臺南分局', '財政部南區國稅局新化分局', '財政部南區國稅局新營分局',
        '財政部南區國稅局嘉義分局', '財政部南區國稅局民雄分局', '財政部南區國稅局朴子分局',
        '財政部南區國稅局屏東分局', '財政部南區國稅局潮州分局', '財政部南區國稅局東港分局',
        '財政部南區國稅局恆春分局', '財政部南區國稅局臺東分局', '財政部南區國稅局成功分局',
        '財政部南區國稅局關山分局', '財政部南區國稅局澎湖分局', '財政部南區國稅局馬公分局',
        '財政部高雄國稅局', '財政部高雄國稅局三民分局', '財政部高雄國稅局新興分局',
        '財政部高雄國稅局前鎮分局', '財政部高雄國稅局苓雅分局', '財政部高雄國稅局小港分局',
        '財政部高雄國稅局楠梓分局', '財政部高雄國稅局岡山分局', '財政部高雄國稅局鳳山分局',
        '財政部高雄國稅局大寮分局', '財政部高雄國稅局林園分局', '財政部高雄國稅局旗山分局',
        '財政部高雄國稅局美濃分局', '財政部高雄國稅局路竹分局', '財政部高雄國稅局湖內分局',
        '財政部高雄國稅局永安分局', '財政部高雄國稅局彌陀分局', '財政部高雄國稅局梓官分局',
        '財政部高雄國稅局橋頭分局', '財政部高雄國稅局燕巢分局', '財政部高雄國稅局田寮分局',
        '財政部高雄國稅局阿蓮分局', '財政部高雄國稅局茄萣分局', '財政部高雄國稅局桃源分局',
        '財政部高雄國稅局那瑪夏分局', '財政部高雄國稅局茂林分局', '財政部高雄國稅局六龜分局',
        '財政部高雄國稅局甲仙分局', '財政部高雄國稅局杉林分局', '財政部高雄國稅局內門分局'
    ]
    
    company_info = None
    
    if request.method == 'POST':
        with get_db() as db:
            company_info = db.query(CompanyInfo).first()
            
            if company_info:
                # 更新現有資料
                company_info.company_name = request.form.get('company_name', '')
                company_info.company_id = request.form.get('company_id', '')
                company_info.owner_name = request.form.get('owner_name', '')
                company_info.owner_phone = request.form.get('owner_phone', '')
                company_info.email = request.form.get('email', '')
                company_info.tax_office = request.form.get('tax_office', '')
                company_info.address = request.form.get('address', '')
                company_info.contact_name = request.form.get('contact_name', '')
                company_info.contact_phone = request.form.get('contact_phone', '')
                company_info.tax_id = request.form.get('tax_id', '')
            else:
                # 建立新資料
                company_info = CompanyInfo(
                    company_name=request.form.get('company_name', ''),
                    company_id=request.form.get('company_id', ''),
                    owner_name=request.form.get('owner_name', ''),
                    owner_phone=request.form.get('owner_phone', ''),
                    email=request.form.get('email', ''),
                    tax_office=request.form.get('tax_office', ''),
                    address=request.form.get('address', ''),
                    contact_name=request.form.get('contact_name', ''),
                    contact_phone=request.form.get('contact_phone', ''),
                    tax_id=request.form.get('tax_id', '')
                )
                db.add(company_info)
        
        return redirect(url_for('settings.basic_info'))
    
    # GET 請求 - 需要將資料轉換為字典
    with get_db() as db:
        company = db.query(CompanyInfo).first()
        if company:
            company_info = {
                'id': company.id,
                'company_name': company.company_name,
                'company_id': company.company_id,
                'owner_name': company.owner_name,
                'owner_phone': company.owner_phone,
                'email': company.email,
                'tax_office': company.tax_office,
                'address': company.address,
                'contact_name': company.contact_name,
                'contact_phone': company.contact_phone,
                'tax_id': company.tax_id
            }
    
    return render_template('basic_info.html', 
                         company_info=company_info, 
                         tax_offices=tax_offices, 
                         sidebar_items=main_menu, 
                         selected=selected)

@settings_bp.route('/account_setting', methods=['GET'])
def account_setting():
    """帳戶設定"""
    main_menu = list(menu.keys())
    selected = '設定'
    tab = request.args.get('tab', '現金')
    return render_template('account_setting.html', 
                         sidebar_items=main_menu, 
                         selected=selected, 
                         tab=tab)

@settings_bp.route('/opening_setting', methods=['GET'])
def opening_setting():
    """開帳設定"""
    main_menu = list(menu.keys())
    selected = '設定'
    def account_to_dict(acc):
        # 預設直接用 subject_code
        full_code = acc.subject_code or ''
        if acc.category == '現金' and acc.subject_code:
            full_code = f'1105{str(acc.subject_code).zfill(3)}'
        elif acc.category == '銀行帳戶' and acc.subject_code:
            full_code = f'1110{str(acc.subject_code).zfill(3)}'
        return {
            'id': acc.id,
            'name': acc.name,
            'subject_code': acc.subject_code,
            'init_amount': acc.init_amount,
            'category': acc.category,
            'full_code': full_code,
        }
    with get_db() as db:
        accounts = db.query(Account).order_by(Account.subject_code.asc()).all()
        accounts = [account_to_dict(acc) for acc in accounts]
    return render_template('opening_setting.html', 
                         sidebar_items=main_menu, 
                         selected=selected,
                         accounts=accounts)

@settings_bp.route('/department_manage', methods=['GET'])
def department_manage():
    """部門管理"""
    main_menu = list(menu.keys())
    selected = '設定'
    with get_db() as db:
        departments = db.query(Department).all()
        departments = [
            {
                'id': dept.id,
                'name': dept.name,
                'parent_name': db.query(Department).filter_by(id=dept.parent_id).first().name if dept.parent_id else '（無）',
                'note': dept.note or ''
            }
            for dept in departments
        ]
    return render_template('department_manage.html', 
                         departments=departments,
                         sidebar_items=main_menu, 
                         selected=selected)

@settings_bp.route('/withholding_declare', methods=['GET'])
def withholding_declare():
    """扣繳申報作業"""
    main_menu = list(menu.keys())
    selected = '扣繳申報'
    return render_template('withholding_declare.html', 
                         sidebar_items=main_menu, 
                         selected=selected)

@settings_bp.route('/payment_identity_list')
def payment_identity_list():
    main_menu = list(menu.keys())
    selected = '設定'
    type_filter = request.args.get('type', '客戶')
    with get_db() as db:
        identities = db.query(PaymentIdentity).filter_by(type=type_filter).all()
        identities = [
            {
                'id': i.id,
                'type': i.type,
                'name': i.name,
                'tax_id': i.tax_id,
                'bank_code': i.bank_code,
                'bank_account': i.bank_account,
                'contact': i.contact,
                'mobile': getattr(i, 'mobile', ''),
                'line': getattr(i, 'line', ''),
                'note': i.note
            }
            for i in identities
        ]
    return render_template(
        'payment_identity_list.html',
        identities=identities,
        sidebar_items=main_menu,
        selected=selected,
        type=type_filter
    )

@settings_bp.route('/payment_identity/edit/<int:pid>', methods=['GET', 'POST'])
def payment_identity_edit(pid):
    main_menu = list(menu.keys())
    selected = '設定'
    with get_db() as db:
        identity = db.query(PaymentIdentity).filter_by(id=pid).first()
        if not identity:
            return "找不到該收支對象", 404
        if request.method == 'GET':
            identity_dict = {
                'id': identity.id,
                'type': identity.type,
                'name': identity.name,
                'tax_id': identity.tax_id,
                'bank_code': identity.bank_code,
                'bank_account': identity.bank_account,
                'contact': identity.contact,
                'mobile': getattr(identity, 'mobile', ''),
                'line': getattr(identity, 'line', ''),
                'note': identity.note
            }
            return render_template(
                'payment_identity_edit.html',
                identity=identity_dict,
                sidebar_items=main_menu,
                selected=selected
            )
        # POST 請求：更新資料並 redirect
        identity.type = request.form.get('type', '')
        identity.name = request.form.get('name', '')
        identity.tax_id = request.form.get('tax_id', '')
        identity.bank_code = request.form.get('bank_code', '')
        identity.bank_account = request.form.get('bank_account', '')
        identity.contact = request.form.get('contact', '')
        identity.mobile = request.form.get('mobile', '')
        identity.line = request.form.get('line', '')
        identity.note = request.form.get('note', '')
        db.commit()
        return redirect(url_for('settings.payment_identity_list'))

@settings_bp.route('/payment_identity/add', methods=['GET', 'POST'])
def payment_identity_add():
    main_menu = list(menu.keys())
    selected = '設定'
    if request.method == 'POST':
        with get_db() as db:
            new_identity = PaymentIdentity(
                type=request.form.get('type', ''),
                name=request.form.get('name', ''),
                tax_id=request.form.get('tax_id', ''),
                bank_code=request.form.get('bank_code', ''),
                bank_account=request.form.get('bank_account', ''),
                contact=request.form.get('contact', ''),
                mobile=request.form.get('mobile', ''),
                line=request.form.get('line', ''),
                note=request.form.get('note', '')
            )
            db.add(new_identity)
            db.commit()
        return redirect(url_for('settings.payment_identity_list'))
    return render_template(
        'payment_identity_add.html',
        sidebar_items=main_menu,
        selected=selected
    )

@settings_bp.route('/department_create', methods=['GET', 'POST'])
def department_create():
    main_menu = list(menu.keys())
    selected = '設定'
    with get_db() as db:
        # 取得所有部門供下拉選單用
        departments = db.query(Department).all()
        if request.method == 'POST':
            new_dept = Department(
                name=request.form.get('name', ''),
                parent_id=request.form.get('parent_id') or None,
                note=request.form.get('note', '')
            )
            db.add(new_dept)
            db.commit()
            return redirect(url_for('settings.department_manage'))
        # GET 請求顯示表單
        return render_template(
            'department_create.html',
            departments=departments,
            sidebar_items=main_menu,
            selected=selected
        )

@settings_bp.route('/project_manage', methods=['GET'])
def project_manage():
    """專案管理"""
    main_menu = list(menu.keys())
    selected = '設定'
    with get_db() as db:
        projects = db.query(Project).all()
        projects = [
            {
                'id': proj.id,
                'name': proj.name,
                'code': proj.code,
                'status': proj.status,
                'department_name': db.query(Department).filter_by(id=proj.department_id).first().name if proj.department_id else '（無）',
                'manager': proj.manager or '（無）',
                'budget': f"{proj.budget:,}" if proj.budget else '（無）',
                'start_date': proj.start_date.strftime('%Y-%m-%d') if proj.start_date else '（無）',
                'end_date': proj.end_date.strftime('%Y-%m-%d') if proj.end_date else '（無）',
                'note': proj.note or ''
            }
            for proj in projects
        ]
    return render_template('project_manage.html', 
                         projects=projects,
                         sidebar_items=main_menu, 
                         selected=selected)

@settings_bp.route('/project_create', methods=['GET', 'POST'])
def project_create():
    """新增專案"""
    main_menu = list(menu.keys())
    selected = '設定'
    with get_db() as db:
        # 取得所有部門供下拉選單用
        departments = db.query(Department).all()
        if request.method == 'POST':
            new_project = Project(
                name=request.form.get('name', ''),
                code=request.form.get('code', ''),
                description=request.form.get('description', ''),
                start_date=parse_date(request.form.get('start_date')),
                end_date=parse_date(request.form.get('end_date')),
                status=request.form.get('status', '進行中'),
                budget=int(request.form.get('budget') or 0),
                department_id=int(request.form.get('department_id')) if request.form.get('department_id') else None,
                manager=request.form.get('manager', ''),
                note=request.form.get('note', '')
            )
            db.add(new_project)
            db.commit()
            return redirect(url_for('settings.project_manage'))
        # GET 請求顯示表單
        return render_template(
            'project_create.html',
            departments=departments,
            sidebar_items=main_menu,
            selected=selected
        )

@settings_bp.route('/project_edit/<int:project_id>', methods=['GET', 'POST'])
def project_edit(project_id):
    """編輯專案"""
    main_menu = list(menu.keys())
    selected = '設定'
    with get_db() as db:
        project = db.query(Project).filter_by(id=project_id).first()
        if not project:
            return redirect(url_for('settings.project_manage'))
        
        departments = db.query(Department).all()
        
        if request.method == 'POST':
            project.name = request.form.get('name', '')
            project.code = request.form.get('code', '')
            project.description = request.form.get('description', '')
            project.start_date = parse_date(request.form.get('start_date'))
            project.end_date = parse_date(request.form.get('end_date'))
            project.status = request.form.get('status', '進行中')
            project.budget = int(request.form.get('budget') or 0)
            project.department_id = int(request.form.get('department_id')) if request.form.get('department_id') else None
            project.manager = request.form.get('manager', '')
            project.note = request.form.get('note', '')
            
            db.commit()
            return redirect(url_for('settings.project_manage'))
        
        return render_template(
            'project_edit.html',
            project=project,
            departments=departments,
            sidebar_items=main_menu,
            selected=selected
        )

@settings_bp.route('/project_delete/<int:project_id>', methods=['POST'])
def project_delete(project_id):
    """刪除專案"""
    with get_db() as db:
        project = db.query(Project).filter_by(id=project_id).first()
        if project:
            db.delete(project)
            db.commit()
    return redirect(url_for('settings.project_manage'))    