from flask import Blueprint, render_template, request, redirect, url_for, flash
from model import Account, AccountSubject
from database import get_db
from utils.helpers import get_template_context
from bank_data import get_head_offices,get_branch_info

account_bp = Blueprint('account', __name__)

def account_to_dict(account):
    """將 Account 物件轉換為字典"""
    return {
        'id': account.id,
        'name': account.name,
        'category': account.category,
        'note': account.note,
        'bank_name': account.bank_name,
        'branch': account.branch,
        'account_number': account.account_number,
        'account_holder': account.account_holder,
        'init_amount': account.init_amount,
        'subject_code': account.subject_code,
        'is_default': account.is_default
    }

def is_standard_bank(bank_name):
    """檢查是否為標準銀行名稱"""
    if not bank_name:
        return False
    standard_banks = list(get_head_offices().values())
    return bank_name in standard_banks

@account_bp.route('/account_setting', methods=['GET'])
def account_setting():
    context = get_template_context('設定')
    return render_template('account_setting.html', **context)

@account_bp.route('/account/cash', methods=['GET'])
def cash_accounts():
    context = get_template_context('設定')
    with get_db() as db:
        cash_accounts = db.query(Account).filter_by(category='現金').all()
        context['accounts'] = [account_to_dict(acc) for acc in cash_accounts]
        context['account_type'] = '現金'
    context['get_head_offices'] = get_head_offices
    context['get_branch_info'] = get_branch_info
    return render_template('account_list.html', **context)

@account_bp.route('/account/bank', methods=['GET'])
def bank_accounts():
    context = get_template_context('設定')
    with get_db() as db:
        bank_accounts = db.query(Account).filter_by(category='銀行帳戶').order_by(Account.is_default.desc(), Account.id.asc()).all()
        context['accounts'] = [account_to_dict(acc) for acc in bank_accounts]
        context['account_type'] = '銀行帳戶'
    context['get_head_offices'] = get_head_offices
    context['get_branch_info'] = get_branch_info
    return render_template('account_list.html', **context)

@account_bp.route('/account/add/cash', methods=['GET', 'POST'])
def add_cash_account():
    context = get_template_context('設定')
    if request.method == 'POST':
        name = request.form.get('name')
        note = request.form.get('note')
        try:
            init_amount = int(request.form.get('init_amount') or 0)
        except ValueError:
            init_amount = 0
        subject_code = request.form.get('subject_code')
        is_default = True if request.form.get('is_default') == 'on' else False
        cover_image = None
        file = request.files.get('cover_image')
        if file and file.filename:
            import os
            upload_dir = os.path.join('static', 'uploads')
            os.makedirs(upload_dir, exist_ok=True)
            file_path = os.path.join(upload_dir, file.filename)
            file.save(file_path)
            cover_image = file.filename
        with get_db() as db:
            new_account = Account(
                name=name,
                category='現金',
                note=note,
                init_amount=init_amount,
                subject_code=subject_code,
                is_default=is_default,
                cover_image=cover_image
            )
            db.add(new_account)
            # 自動新增子科目
            if init_amount:
                parent = db.query(AccountSubject).filter_by(code='1105').first()
                full_code = f'1105{subject_code}'
                exist = db.query(AccountSubject).filter_by(code=full_code).first()
                if parent and not exist:
                    child = AccountSubject(
                        name=name,
                        code=full_code,
                        parent_id=parent.id,
                        top_category=parent.top_category
                    )
                    db.add(child)
        flash('現金帳戶新增成功', 'success')
        return redirect(url_for('account.cash_accounts'))
    return render_template('add_cash_account.html', **context)

@account_bp.route('/account/add/bank', methods=['GET', 'POST'])
def add_bank_account():
    context = get_template_context('設定')
    #context['bank_options'] = get_bank_options()
    
    if request.method == 'POST':
        name = request.form.get('name')
        note = request.form.get('note')
        bank_name = request.form.get('bank_name')
        branch = request.form.get('branch')
        account_number = request.form.get('account_number')
        account_holder = request.form.get('account_holder')
        subject_code = request.form.get('subject_code')
        # 處理「其他」選項
        if bank_name == '其他':
            bank_name = request.form.get('other_bank_name')
        with get_db() as db:
            new_account = Account(
                name=name,
                category='銀行帳戶',
                note=note,
                bank_name=bank_name,
                branch=branch,
                account_number=account_number,
                account_holder=account_holder,
                subject_code=subject_code
            )
            db.add(new_account)
            # 自動新增子科目
            if subject_code:
                parent = db.query(AccountSubject).filter_by(code='1110').first()
                full_code = f'1110{subject_code}'
                exist = db.query(AccountSubject).filter_by(code=full_code).first()
                if parent and not exist:
                    child = AccountSubject(
                        name=name,
                        code=full_code,
                        parent_id=parent.id,
                        top_category=parent.top_category
                    )
                    db.add(child)
        flash('銀行帳戶新增成功', 'success')
        return redirect(url_for('account.bank_accounts'))
    return render_template('add_bank_account.html', **context)

@account_bp.route('/account/edit/<int:account_id>', methods=['GET', 'POST'])
def edit_account(account_id):
    context = get_template_context('設定')
    #context['bank_options'] = get_bank_options()
    
    with get_db() as db:
        account = db.query(Account).filter_by(id=account_id).first()
        if not account:
            flash('找不到該帳戶', 'error')
            return redirect(url_for('account.account_setting'))
        
        if request.method == 'POST':
            account.name = request.form.get('name')
            account.note = request.form.get('note')
            
            if account.category == '銀行帳戶':
                bank_name = request.form.get('bank_name')
                if bank_name == '其他':
                    bank_name = request.form.get('other_bank_name')
                account.bank_name = bank_name
                account.branch = request.form.get('branch')
                account.account_number = request.form.get('account_number')
                account.account_holder = request.form.get('account_holder')
                account.subject_code = request.form.get('subject_code')
                account.is_default = True if request.form.get('is_default') == 'on' else False
                try:
                    account.init_amount = int(request.form.get('init_amount') or 0)
                except ValueError:
                    account.init_amount = 0
            elif account.category == '現金':
                try:
                    account.init_amount = int(request.form.get('init_amount') or 0)
                except ValueError:
                    account.init_amount = 0
                account.subject_code = request.form.get('subject_code')
                account.is_default = True if request.form.get('is_default') == 'on' else False
                # 自動新增子科目
                if account.init_amount:
                    parent = db.query(AccountSubject).filter_by(code='1105').first()
                    full_code = f'1105{account.subject_code}'
                    exist = db.query(AccountSubject).filter_by(code=full_code).first()
                    if parent and not exist:
                        child = AccountSubject(
                            name=account.name,
                            code=full_code,
                            parent_id=parent.id,
                            top_category=parent.top_category
                        )
                        db.add(child)
            db.commit()
            flash('帳戶更新成功', 'success')
            if account.category == '現金':
                return redirect(url_for('account.cash_accounts'))
            else:
                return redirect(url_for('account.bank_accounts'))
        
        # 準備編輯用的資料
        account_dict = account_to_dict(account)
        if account.category == '銀行帳戶':
            # 檢查是否為標準銀行，如果不是則設定為「其他」
            if not is_standard_bank(account.bank_name):
                account_dict['is_other_bank'] = True
                account_dict['other_bank_name'] = account.bank_name
            else:
                account_dict['is_other_bank'] = False
        
        context['account'] = account_dict
    return render_template('edit_account.html', **context)

@account_bp.route('/account/delete/<int:account_id>', methods=['POST'])
def delete_account(account_id):
    with get_db() as db:
        account = db.query(Account).filter_by(id=account_id).first()
        if account:
            category = account.category
            db.delete(account)
            flash('帳戶刪除成功', 'success')
            if category == '現金':
                return redirect(url_for('account.cash_accounts'))
            else:
                return redirect(url_for('account.bank_accounts'))
        else:
            flash('找不到該帳戶', 'error')
            return redirect(url_for('account.account_setting')) 