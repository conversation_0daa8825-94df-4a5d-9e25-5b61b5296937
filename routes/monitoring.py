"""監控相關路由"""
from flask import Blueprint, render_template, jsonify, request
import os
import time
import psutil
from datetime import datetime, timedelta, timezone
from utils.performance_monitor import performance_monitor
from utils.helpers import get_template_context

monitoring_bp = Blueprint('monitoring', __name__)


@monitoring_bp.route('/admin/monitoring')
def monitoring_dashboard():
    """監控儀表板"""
    context = get_template_context('系統監控')
    
    # 獲取系統資訊
    system_info = {
        'cpu_percent': psutil.cpu_percent(interval=1),
        'memory': psutil.virtual_memory(),
        'disk': psutil.disk_usage('.'),
        'boot_time': datetime.fromtimestamp(psutil.boot_time())
    }
    
    # 獲取性能統計
    perf_stats = performance_monitor.get_stats()
    
    # 獲取日誌文件資訊
    log_files = get_log_files_info()
    
    context.update({
        'system_info': system_info,
        'perf_stats': perf_stats,
        'log_files': log_files
    })
    
    return render_template('monitoring/dashboard.html', **context)


@monitoring_bp.route('/admin/monitoring/api/stats')
def api_stats():
    """API統計數據"""
    stats = performance_monitor.get_stats()
    
    # 添加系統資訊
    stats.update({
        'cpu_percent': psutil.cpu_percent(),
        'memory_percent': psutil.virtual_memory().percent,
        'disk_percent': (psutil.disk_usage('.').used / psutil.disk_usage('.').total) * 100,
        'timestamp': time.time()
    })
    
    return jsonify(stats)


@monitoring_bp.route('/admin/monitoring/logs')
def view_logs():
    """查看日誌"""
    context = get_template_context('日誌查看')
    
    log_type = request.args.get('type', 'accounting')
    lines = int(request.args.get('lines', 100))
    
    log_content = get_log_content(log_type, lines)
    log_files = get_log_files_info()
    
    context.update({
        'log_content': log_content,
        'log_files': log_files,
        'current_log': log_type,
        'lines': lines
    })
    
    return render_template('monitoring/logs.html', **context)


@monitoring_bp.route('/admin/monitoring/api/logs')
def api_logs():
    """API獲取日誌"""
    log_type = request.args.get('type', 'accounting')
    lines = int(request.args.get('lines', 50))
    
    log_content = get_log_content(log_type, lines)
    
    return jsonify({
        'content': log_content,
        'type': log_type,
        'timestamp': time.time()
    })


def get_log_files_info():
    """獲取日誌文件資訊"""
    log_dir = 'logs'
    log_files = []
    
    if os.path.exists(log_dir):
        for filename in os.listdir(log_dir):
            if filename.endswith('.log'):
                filepath = os.path.join(log_dir, filename)
                stat = os.stat(filepath)
                
                log_files.append({
                    'name': filename,
                    'size': stat.st_size,
                    'size_mb': round(stat.st_size / 1024 / 1024, 2),
                    'modified': datetime.fromtimestamp(stat.st_mtime),
                    'type': filename.replace('.log', '')
                })
    
    return sorted(log_files, key=lambda x: x['modified'], reverse=True)


def get_log_content(log_type, lines=100):
    """獲取日誌內容"""
    log_file = f'logs/{log_type}.log'
    
    if not os.path.exists(log_file):
        return f"日誌文件 {log_file} 不存在"
    
    try:
        # 讀取最後N行
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            return ''.join(all_lines[-lines:])
    except Exception as e:
        return f"讀取日誌文件錯誤: {e}"


@monitoring_bp.route('/admin/monitoring/health')
def health_check():
    """健康檢查端點"""
    try:
        # 檢查資料庫連接
        from database import get_db
        with get_db() as db:
            db.execute("SELECT 1").fetchone()
        
        # 檢查系統資源
        cpu_ok = psutil.cpu_percent() < 90
        memory_ok = psutil.virtual_memory().percent < 90
        disk_ok = (psutil.disk_usage('.').used / psutil.disk_usage('.').total) * 100 < 95
        
        health_status = {
            'status': 'healthy' if all([cpu_ok, memory_ok, disk_ok]) else 'warning',
            'timestamp': datetime.now(timezone(timedelta(hours=8))).replace(microsecond=0, tzinfo=None).isoformat(),
            'checks': {
                'database': 'ok',
                'cpu': 'ok' if cpu_ok else 'warning',
                'memory': 'ok' if memory_ok else 'warning',
                'disk': 'ok' if disk_ok else 'warning'
            },
            'metrics': {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': (psutil.disk_usage('.').used / psutil.disk_usage('.').total) * 100
            }
        }
        
        status_code = 200 if health_status['status'] == 'healthy' else 503
        return jsonify(health_status), status_code
        
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'timestamp': datetime.now(timezone(timedelta(hours=8))).replace(microsecond=0, tzinfo=None).isoformat(),
            'error': str(e)
        }), 503