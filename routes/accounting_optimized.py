from flask import Blueprint, render_template, request, redirect, url_for, flash
from services.subject_service import SubjectService

accounting_bp = Blueprint('accounting', __name__)

@accounting_bp.route('/subject_manage')
def subject_manage():
    """科目管理頁面"""
    try:
        category_dict = SubjectService.get_all_subjects_by_category()
        return render_template('subject_manage.html', 
                             category_dict=category_dict,
                             selected='會計科目')
    except Exception as e:
        flash(f'載入科目資料時發生錯誤: {str(e)}', 'error')
        return render_template('subject_manage.html', 
                             category_dict={},
                             selected='會計科目')

@accounting_bp.route('/add_subject', methods=['GET', 'POST'])
def add_subject():
    """新增科目"""
    if request.method == 'POST':
        return _handle_add_subject_post()
    else:
        return _handle_add_subject_get()

def _handle_add_subject_post():
    """處理新增科目的 POST 請求"""
    try:
        sub_name = request.form.get('sub_name')
        sub_code = request.form.get('sub_code')
        sub_note = request.form.get('sub_note')
        parent_code = request.form.get('parent_code')
        
        if not sub_name or not sub_code:
            flash('科目名稱和代碼為必填欄位', 'error')
            return redirect(url_for('accounting.add_subject', parent_code=parent_code))
        
        SubjectService.create_subject(
            name=sub_name,
            code=sub_code,
            note=sub_note,
            parent_code=parent_code
        )
        flash('科目新增成功', 'success')
        
    except Exception as e:
        flash(f'新增科目時發生錯誤: {str(e)}', 'error')
    
    return redirect(url_for('accounting.subject_manage'))

def _handle_add_subject_get():
    """處理新增科目的 GET 請求"""
    parent_code = request.args.get('parent_code')
    parent_info = None
    next_sub_code = '001'
    
    if parent_code:
        parent_info = SubjectService.get_subject_by_code(parent_code)
        if parent_info:
            next_sub_code = SubjectService.get_next_sub_code(parent_code)
    
    return render_template('add_subject.html', 
                         parent_info=parent_info, 
                         next_sub_code=next_sub_code,
                         selected='會計科目')

@accounting_bp.route('/edit_subject', methods=['GET', 'POST'])
def edit_subject():
    """編輯科目"""
    code = request.args.get('code') if request.method == 'GET' else request.form.get('code')
    
    if request.method == 'POST':
        try:
            sub_name = request.form.get('sub_name')
            sub_note = request.form.get('sub_note')
            
            if not sub_name:
                flash('科目名稱為必填欄位', 'error')
                return redirect(url_for('accounting.edit_subject', code=code))
            
            success = SubjectService.update_subject(code, sub_name, sub_note)
            if success:
                flash('科目更新成功', 'success')
            else:
                flash('找不到該科目', 'error')
                
        except Exception as e:
            flash(f'更新科目時發生錯誤: {str(e)}', 'error')
        
        return redirect(url_for('accounting.subject_manage'))
    
    # GET 請求
    subject = SubjectService.get_subject_by_code(code)
    if not subject:
        flash('找不到該科目', 'error')
        return redirect(url_for('accounting.subject_manage'))
    
    return render_template('edit_subject.html', 
                         subject=subject,
                         selected='會計科目')

@accounting_bp.route('/delete_subject', methods=['POST'])
def delete_subject():
    """刪除科目"""
    try:
        code = request.form.get('code')
        success = SubjectService.delete_subject(code)
        
        if success:
            flash('科目刪除成功', 'success')
        else:
            flash('找不到該科目', 'error')
            
    except Exception as e:
        flash(f'刪除科目時發生錯誤: {str(e)}', 'error')
    
    return redirect(url_for('accounting.subject_manage')) 