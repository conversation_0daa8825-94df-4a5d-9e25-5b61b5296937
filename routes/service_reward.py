from flask import Blueprint, render_template, request, redirect, url_for, flash
from data.menu_data import menu
from model import Account, session, Department, Project
import os
from werkzeug.utils import secure_filename

service_reward_bp = Blueprint('service_reward', __name__)

@service_reward_bp.route('/service_reward_list', methods=['GET'])
def service_reward_list():
    """勞務報酬列表"""
    main_menu = list(menu.keys())
    selected = '勞務報酬'
    return render_template('service_reward_list.html', 
                         sidebar_items=main_menu, 
                         selected=selected)

@service_reward_bp.route('/add_service_reward', methods=['GET', 'POST'])
def add_service_reward():
    """建立勞報單"""
    if request.method == 'POST':
        try:
            # 獲取表單類型
            form_type = request.form.get('form_type', 'company')
            action = request.form.get('action', 'submit')

            # 基本資料
            create_date = request.form.get('create_date')
            name = request.form.get('name')
            email = request.form.get('email')
            phone = request.form.get('phone')

            # 勞務資訊
            nationality = request.form.get('nationality')
            no_health_insurance = request.form.get('no_health_insurance')
            declaration_type = request.form.get('declaration_type')
            confirmation_method = request.form.get('confirmation_method')
            service_start_date = request.form.get('service_start_date')
            service_end_date = request.form.get('service_end_date')
            service_content = request.form.get('service_content')

            # 付款資訊
            payment_account = request.form.get('payment_account')
            payable_date = request.form.get('payable_date')
            payment_amount = request.form.get('payment_amount')
            actual_amount = request.form.get('actual_amount')

            # 所有人提供資料
            owner_bank_code = request.form.get('owner_bank_code')
            owner_bank_account = request.form.get('owner_bank_account')
            owner_account_name = request.form.get('owner_account_name')

            # 其他資訊
            department_id = request.form.get('department_id')
            project_id = request.form.get('project_id')
            notice = request.form.get('notice')
            note = request.form.get('note')

            # 處理公司填寫特有欄位
            if form_type == 'company':
                id_number = request.form.get('id_number')
                address = request.form.get('address')
                print(f"公司填寫 - 身份證號: {id_number}, 戶籍地址: {address}")
            else:
                print("個人填寫 - 無身份證號和戶籍地址")

            # 處理檔案上傳
            upload_folder = 'static/uploads/service_reward'
            os.makedirs(upload_folder, exist_ok=True)

            uploaded_files = {}
            for file_field in ['owner_passbook', 'owner_id_front', 'owner_id_back']:
                if file_field in request.files:
                    file = request.files[file_field]
                    if file and file.filename:
                        filename = secure_filename(file.filename)
                        file_path = os.path.join(upload_folder, filename)
                        file.save(file_path)
                        uploaded_files[file_field] = filename

            # 這裡可以將資料儲存到資料庫
            # TODO: 建立 ServiceReward 模型並儲存資料

            if action == 'save_exit':
                flash(f'勞務報酬單已暫存（{form_type}填寫）！', 'success')
            else:
                flash(f'勞務報酬單建立完成（{form_type}填寫）！', 'success')

            return redirect(url_for('service_reward.service_reward_list'))

        except Exception as e:
            flash(f'處理失敗：{str(e)}', 'error')
            print(f"Error: {e}")

    # GET 請求
    accounts = session.query(Account).all()
    departments = session.query(Department).all()
    projects = session.query(Project).all()
    main_menu = list(menu.keys())
    selected = '勞務報酬'
    return render_template('add_service_reward.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         accounts=accounts,
                         departments=departments,
                         projects=projects)