from flask import Blueprint, render_template, request
from data.menu_data import menu

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """主頁面"""
    main_menu = list(menu.keys())
    selected = request.args.get('main', main_menu[0])
    submenus = menu[selected]
    submenu_title = request.args.get('submenu')
    button_label = request.args.get('button')
    selected_buttons = None
    
    if submenu_title and button_label:
        # 找到該submenu下的button
        for submenu in submenus:
            if submenu['title'] == submenu_title:
                for btn in submenu['buttons']:
                    if btn['label'] == button_label:
                        selected_buttons = btn['children']
                        break
    
    return render_template('index.html', 
                        sidebar_items=main_menu, 
                        submenus=submenus, 
                        selected=selected, 
                        submenu_title=submenu_title, 
                        button_label=button_label, 
                        selected_buttons=selected_buttons) 
    
    
@main_bp.route('/start')
def start():
    """起始頁面"""
    return render_template('start.html')

@main_bp.route('/fund_manage')
def fund_manage():
    """資金管理主頁"""
    main_menu = list(menu.keys())
    selected = '資金管理'
    submenus = menu[selected]
    return render_template('index.html', 
        sidebar_items=main_menu, 
        submenus=submenus, 
        selected=selected)