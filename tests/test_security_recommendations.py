"""安全性改進建議實施測試"""
import pytest
from app import create_app


@pytest.fixture
def secure_app():
    """創建帶有安全改進的測試應用"""
    app = create_app()
    
    # 添加安全標頭
    @app.after_request
    def add_security_headers(response):
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        response.headers['Content-Security-Policy'] = "default-src 'self'"
        return response
    
    app.config.update({
        'TESTING': True,
        'WTF_CSRF_ENABLED': True,  # 啟用 CSRF 保護
        'SECRET_KEY': 'test-secret-key-for-csrf'
    })
    
    return app


class TestSecurityHeaders:
    """測試安全標頭實施"""
    
    def test_security_headers_implemented(self, secure_app):
        """測試安全標頭是否正確實施"""
        client = secure_app.test_client()
        response = client.get('/')
        
        # 檢查所有重要的安全標頭
        expected_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'"
        }
        
        for header, expected_value in expected_headers.items():
            assert header in response.headers, f"缺少安全標頭: {header}"
            assert response.headers[header] == expected_value, \
                f"安全標頭值不正確: {header} = {response.headers[header]}"
            print(f"✅ {header}: {response.headers[header]}")


class TestInputValidationEnhancements:
    """測試增強的輸入驗證"""
    
    def test_account_code_format_validation(self):
        """測試科目代碼格式驗證"""
        import re
        
        def validate_account_code(code):
            """驗證科目代碼格式"""
            if not code:
                raise ValueError("科目代碼不能為空")
            
            if len(code) > 20:
                raise ValueError("科目代碼長度不能超過 20 字符")
            
            # 只允許數字和字母
            if not re.match(r'^[A-Za-z0-9]+$', code):
                raise ValueError("科目代碼只能包含字母和數字")
            
            return code
        
        # 測試有效的代碼
        valid_codes = ['1111', 'ASSET001', 'A1B2C3', '123ABC']
        for code in valid_codes:
            assert validate_account_code(code) == code
        
        # 測試無效的代碼
        invalid_codes = [
            '',  # 空字符串
            'A' * 21,  # 太長
            "'; DROP TABLE users; --",  # SQL 注入
            '1111-ABC',  # 包含特殊字符
            '1111 ABC',  # 包含空格
            '<script>',  # XSS 攻擊
        ]
        
        for code in invalid_codes:
            with pytest.raises(ValueError):
                validate_account_code(code)
            print(f"✅ 正確拒絕無效代碼: {code}")
    
    def test_amount_validation(self):
        """測試金額驗證"""
        def validate_amount(amount_str):
            """驗證金額格式"""
            if not amount_str:
                return 0
            
            # 移除可能的惡意字符
            cleaned = ''.join(c for c in amount_str if c.isdigit() or c in '.-')
            
            try:
                amount = float(cleaned)
                if amount < 0:
                    raise ValueError("金額不能為負數")
                if amount > *********:  # 設定合理上限
                    raise ValueError("金額過大")
                return int(amount)  # 轉為整數（分為單位）
            except ValueError:
                raise ValueError("金額格式不正確")
        
        # 測試有效金額
        valid_amounts = ['1000', '1000.50', '0', '999999']
        for amount in valid_amounts:
            result = validate_amount(amount)
            assert isinstance(result, int)
            assert result >= 0
        
        # 測試無效金額
        invalid_amounts = [
            "'; DROP TABLE users; --",
            '<script>alert("XSS")</script>',
            '1000; DELETE FROM users;',
            'abc',
            '-1000',  # 負數
            '*********9',  # 太大
        ]
        
        for amount in invalid_amounts:
            with pytest.raises(ValueError):
                validate_amount(amount)
            print(f"✅ 正確拒絕無效金額: {amount}")


class TestCSRFProtection:
    """測試 CSRF 保護"""
    
    def test_csrf_token_required(self, secure_app):
        """測試 CSRF 令牌是否必需"""
        client = secure_app.test_client()
        
        # 嘗試不帶 CSRF 令牌的 POST 請求
        response = client.post('/accounting/add_subject', data={
            'sub_name': '測試科目',
            'sub_code': '9999',
            'sub_style': '資產',
            'top_category': '資產'
        })
        
        # 應該被 CSRF 保護拒絕
        assert response.status_code in [400, 403], "CSRF 保護未生效"
        print("✅ CSRF 保護正常運作")


class TestSecurityLogging:
    """測試安全日誌記錄"""
    
    def test_suspicious_input_logging(self):
        """測試可疑輸入的日誌記錄"""
        import logging
        from io import StringIO
        
        # 設置日誌捕獲
        log_capture = StringIO()
        handler = logging.StreamHandler(log_capture)
        logger = logging.getLogger('security')
        logger.addHandler(handler)
        logger.setLevel(logging.WARNING)
        
        def log_suspicious_input(input_value, source):
            """記錄可疑輸入"""
            suspicious_patterns = [
                'DROP TABLE',
                'DELETE FROM',
                'INSERT INTO',
                'UNION SELECT',
                '<script>',
                'javascript:',
                '../',
                'etc/passwd'
            ]
            
            for pattern in suspicious_patterns:
                if pattern.lower() in input_value.lower():
                    logger.warning(f"可疑輸入檢測: {pattern} 在 {source} 中發現: {input_value[:50]}")
                    return True
            return False
        
        # 測試可疑輸入檢測
        suspicious_inputs = [
            ("'; DROP TABLE users; --", "科目代碼"),
            ("<script>alert('XSS')</script>", "科目名稱"),
            ("../../../etc/passwd", "檔案路徑"),
            ("1' UNION SELECT * FROM users --", "查詢參數")
        ]
        
        for input_value, source in suspicious_inputs:
            detected = log_suspicious_input(input_value, source)
            assert detected, f"未檢測到可疑輸入: {input_value}"
        
        # 檢查日誌內容
        log_content = log_capture.getvalue()
        assert '可疑輸入檢測' in log_content
        print("✅ 安全日誌記錄正常運作")


class TestRateLimiting:
    """測試速率限制"""
    
    def test_rate_limiting_concept(self):
        """測試速率限制概念實現"""
        from collections import defaultdict
        import time
        
        class SimpleRateLimiter:
            def __init__(self, max_requests=10, time_window=60):
                self.max_requests = max_requests
                self.time_window = time_window
                self.requests = defaultdict(list)
            
            def is_allowed(self, client_ip):
                now = time.time()
                # 清理過期的請求記錄
                self.requests[client_ip] = [
                    req_time for req_time in self.requests[client_ip]
                    if now - req_time < self.time_window
                ]
                
                # 檢查是否超過限制
                if len(self.requests[client_ip]) >= self.max_requests:
                    return False
                
                # 記錄新請求
                self.requests[client_ip].append(now)
                return True
        
        # 測試速率限制
        limiter = SimpleRateLimiter(max_requests=3, time_window=60)
        client_ip = "*************"
        
        # 前 3 個請求應該被允許
        for i in range(3):
            assert limiter.is_allowed(client_ip), f"第 {i+1} 個請求應該被允許"
        
        # 第 4 個請求應該被拒絕
        assert not limiter.is_allowed(client_ip), "第 4 個請求應該被拒絕"
        
        print("✅ 速率限制機制正常運作")


class TestSecurityBestPractices:
    """測試安全最佳實踐"""
    
    def test_password_hashing_concept(self):
        """測試密碼雜湊概念"""
        import hashlib
        import secrets
        
        def hash_password(password):
            """安全的密碼雜湊"""
            # 生成隨機鹽值
            salt = secrets.token_hex(16)
            # 使用 PBKDF2 進行雜湊
            password_hash = hashlib.pbkdf2_hmac('sha256', 
                                               password.encode('utf-8'), 
                                               salt.encode('utf-8'), 
                                               100000)  # 100,000 次迭代
            return salt + password_hash.hex()
        
        def verify_password(password, stored_hash):
            """驗證密碼"""
            salt = stored_hash[:32]  # 前 32 字符是鹽值
            stored_password_hash = stored_hash[32:]
            
            password_hash = hashlib.pbkdf2_hmac('sha256',
                                               password.encode('utf-8'),
                                               salt.encode('utf-8'),
                                               100000)
            return password_hash.hex() == stored_password_hash
        
        # 測試密碼雜湊
        password = "secure_password_123"
        hashed = hash_password(password)
        
        assert len(hashed) > 32, "雜湊長度不正確"
        assert verify_password(password, hashed), "密碼驗證失敗"
        assert not verify_password("wrong_password", hashed), "錯誤密碼不應該通過驗證"
        
        print("✅ 密碼雜湊機制正常運作")
    
    def test_session_security_concept(self):
        """測試會話安全概念"""
        import secrets
        import time
        
        class SecureSession:
            def __init__(self, timeout=3600):  # 1 小時超時
                self.sessions = {}
                self.timeout = timeout
            
            def create_session(self, user_id):
                """創建安全會話"""
                session_id = secrets.token_urlsafe(32)
                self.sessions[session_id] = {
                    'user_id': user_id,
                    'created_at': time.time(),
                    'last_activity': time.time()
                }
                return session_id
            
            def validate_session(self, session_id):
                """驗證會話"""
                if session_id not in self.sessions:
                    return False
                
                session = self.sessions[session_id]
                now = time.time()
                
                # 檢查會話是否過期
                if now - session['last_activity'] > self.timeout:
                    del self.sessions[session_id]
                    return False
                
                # 更新最後活動時間
                session['last_activity'] = now
                return True
            
            def destroy_session(self, session_id):
                """銷毀會話"""
                if session_id in self.sessions:
                    del self.sessions[session_id]
        
        # 測試會話管理
        session_manager = SecureSession(timeout=1)  # 1 秒超時用於測試
        
        # 創建會話
        session_id = session_manager.create_session("user123")
        assert len(session_id) > 20, "會話 ID 長度不足"
        
        # 驗證會話
        assert session_manager.validate_session(session_id), "會話驗證失敗"
        
        # 等待超時
        time.sleep(1.1)
        assert not session_manager.validate_session(session_id), "過期會話不應該有效"
        
        print("✅ 會話安全機制正常運作")