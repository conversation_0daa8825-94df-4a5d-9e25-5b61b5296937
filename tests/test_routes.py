import pytest
from app import create_app

EXCLUDE_URLS = ['/accounting/edit_subject']

@pytest.fixture
def client():
    app = create_app()
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client

def test_all_get_routes(client):
    """自動測試所有無參數的 GET 路由是否能正確回應"""
    app = client.application
    failed = []
    for rule in app.url_map.iter_rules():
        if 'GET' in rule.methods and len(rule.arguments) == 0:
            url = rule.rule
            if url in EXCLUDE_URLS:
                continue
            response = client.get(url)
            if response.status_code not in (200, 302):
                failed.append((url, response.status_code))
    assert not failed, f"以下路由回應非 200/302: {failed}" 