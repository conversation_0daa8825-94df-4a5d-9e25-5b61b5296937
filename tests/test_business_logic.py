"""測試業務邏輯"""
import pytest
import tempfile
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from model import Base, AccountSubject


@pytest.fixture
def test_db():
    """創建測試用的臨時資料庫"""
    db_fd, db_path = tempfile.mkstemp()
    test_engine = create_engine(f'sqlite:///{db_path}')
    Base.metadata.create_all(test_engine)
    
    TestSession = sessionmaker(bind=test_engine)
    session = TestSession()
    
    yield session
    
    # 清理
    session.close()
    os.close(db_fd)
    os.unlink(db_path)


class TestAccountSubjectLogic:
    """測試會計科目業務邏輯"""
    
    def test_generate_next_sub_code(self, test_db):
        """測試生成下一個子科目代碼的邏輯"""
        # 創建父科目
        parent = AccountSubject(name='流動資產', code='11', style='資產', top_category='資產')
        test_db.add(parent)
        test_db.commit()
        
        # 創建一些子科目
        child1 = AccountSubject(name='現金', code='11001', style='資產', top_category='資產', parent_id=parent.id)
        child2 = AccountSubject(name='銀行存款', code='11002', style='資產', top_category='資產', parent_id=parent.id)
        child3 = AccountSubject(name='應收帳款', code='11005', style='資產', top_category='資產', parent_id=parent.id)
        
        test_db.add_all([child1, child2, child3])
        test_db.commit()
        
        # 模擬 main.py 中生成下一個代碼的邏輯
        used_codes = set()
        for child in parent.children:
            try:
                code_int = int(child.code[-3:])  # 只取最後三碼
                used_codes.add(code_int)
            except ValueError:
                pass
        
        next_sub_code = None
        for i in range(1, 1000):
            code_str = f"{i:03d}"
            if int(code_str) not in used_codes:
                next_sub_code = code_str
                break
        
        # 應該生成 003（因為 001, 002, 005 已被使用）
        assert next_sub_code == "003"
    
    def test_account_subject_hierarchy_validation(self, test_db):
        """測試會計科目階層驗證"""
        # 測試不能將科目設為自己的父科目
        subject = AccountSubject(name='測試科目', code='1111', style='資產', top_category='資產')
        test_db.add(subject)
        test_db.commit()
        
        # 這在實際應用中應該被阻止
        subject.parent_id = subject.id
        # 注意：這裡只是演示，實際應用中需要在業務邏輯層面防止這種情況
    
    def test_account_subject_code_format(self, test_db):
        """測試會計科目代碼格式"""
        # 測試各種代碼格式
        test_cases = [
            ('1', '資產'),
            ('11', '資產'),
            ('1111', '資產'),
            ('11001', '資產'),
            ('2', '負債'),
            ('21', '負債'),
            ('2111', '負債'),
        ]
        
        for code, style in test_cases:
            subject = AccountSubject(
                name=f'測試科目{code}',
                code=code,
                style=style,
                top_category=style
            )
            test_db.add(subject)
        
        test_db.commit()
        
        # 驗證所有科目都成功創建
        subjects = test_db.query(AccountSubject).all()
        assert len(subjects) == len(test_cases)


class TestAccountingRules:
    """測試會計規則"""
    
    def test_account_subject_categories(self, test_db):
        """測試會計科目分類規則"""
        # 定義標準會計科目分類
        categories = {
            '1': '資產',
            '2': '負債', 
            '3': '權益',
            '4': '收入',
            '5': '費用'
        }
        
        for code, category in categories.items():
            subject = AccountSubject(
                name=f'{category}類',
                code=code,
                style=category,
                top_category=category
            )
            test_db.add(subject)
        
        test_db.commit()
        
        # 驗證分類正確性
        for code, expected_category in categories.items():
            subject = test_db.query(AccountSubject).filter_by(code=code).first()
            assert subject is not None
            assert subject.style == expected_category
            assert subject.top_category == expected_category
    
    def test_asset_subcategories(self, test_db):
        """測試資產子分類"""
        # 創建資產主分類
        asset_main = AccountSubject(name='資產', code='1', style='資產', top_category='資產')
        test_db.add(asset_main)
        test_db.commit()
        
        # 創建資產子分類
        subcategories = [
            ('11', '流動資產'),
            ('12', '非流動資產'),
            ('13', '其他資產')
        ]
        
        for code, name in subcategories:
            subject = AccountSubject(
                name=name,
                code=code,
                style='資產',
                top_category='資產',
                parent_id=asset_main.id
            )
            test_db.add(subject)
        
        test_db.commit()
        
        # 驗證子分類都屬於資產
        for code, name in subcategories:
            subject = test_db.query(AccountSubject).filter_by(code=code).first()
            assert subject.parent == asset_main
            assert subject.top_category == '資產'


class TestDataValidation:
    """測試資料驗證"""
    
    def test_required_fields(self, test_db):
        """測試必填欄位驗證"""
        # 測試缺少必填欄位時的行為
        with pytest.raises(Exception):
            # 缺少 name
            subject = AccountSubject(code='1111', style='資產')
            test_db.add(subject)
            test_db.commit()
    
    def test_code_uniqueness(self, test_db):
        """測試科目代碼唯一性（如果有設定的話）"""
        subject1 = AccountSubject(name='科目1', code='1111', style='資產', top_category='資產')
        subject2 = AccountSubject(name='科目2', code='1111', style='資產', top_category='資產')
        
        test_db.add(subject1)
        test_db.commit()
        
        test_db.add(subject2)
        # 注意：目前模型中沒有設定 unique=True，所以這不會失敗
        # 但在實際應用中可能需要業務邏輯層面的驗證
        test_db.commit()