# 測試指南

## 測試結構

我們已經為您的會計系統創建了完整的測試套件，包含以下類型的測試：

### 1. 模型測試 (`test_models.py`)
- ✅ **全部通過** - 測試資料模型的創建、關係和驗證
- 測試內容：User, AccountSubject, CompanyInfo, Account, PaymentIdentity, Department

### 2. 業務邏輯測試 (`test_business_logic.py`)
- ✅ **全部通過** - 測試核心業務邏輯
- 測試內容：科目代碼生成、會計規則、資料驗證

### 3. 路由測試 (`test_routes.py`)
- ✅ **通過** - 原有的路由測試

### 4. API 端點測試 (`test_api_endpoints.py`)
- ⚠️ **部分失敗** - 一些路由返回 404，需要檢查路由配置

### 5. 整合測試 (`test_integration.py`)
- ⚠️ **部分失敗** - 依賴於 API 端點測試

### 6. 工具函數測試 (`test_utils.py`)
- ✅ **全部通過** - 測試輔助函數（目前為空實現）

## 測試運行方法

### 運行所有測試
```bash
python -m pytest tests/ -v
```

### 運行特定測試文件
```bash
python -m pytest tests/test_models.py -v
python -m pytest tests/test_business_logic.py -v
```

### 運行特定測試類
```bash
python -m pytest tests/test_models.py::TestUser -v
```

### 運行特定測試方法
```bash
python -m pytest tests/test_models.py::TestUser::test_create_user -v
```

### 顯示測試覆蓋率
```bash
pip install pytest-cov
python -m pytest tests/ --cov=. --cov-report=html
```

## 測試結果分析

### ✅ 成功的測試 (44/62 通過)
- **模型測試**: 所有資料模型都正常工作
- **業務邏輯**: 核心會計邏輯正確
- **部分路由**: 收支、設定、薪資、資產相關路由正常

### ⚠️ 失敗的測試 (18/62 失敗)
主要問題：
1. **路由 404 錯誤**: 某些路由在測試環境中找不到
2. **應用配置**: 測試用的 Flask 應用配置可能需要調整

## 修復建議

### 1. 檢查路由配置
```python
# 在 app.py 中確認所有路由都已註冊
from routes.main import main_bp
from routes.account import account_bp
# ... 其他路由

app.register_blueprint(main_bp)
app.register_blueprint(account_bp)
```

### 2. 更新測試配置
```python
# 在 conftest.py 中改進應用配置
@pytest.fixture
def app():
    app = create_app()
    app.config.update({
        'TESTING': True,
        'WTF_CSRF_ENABLED': False,
        'DATABASE_URI': 'sqlite:///:memory:'
    })
    return app
```

### 3. 修復具體路由測試
某些路由可能需要：
- 正確的 URL 路徑
- 必要的參數
- 適當的 HTTP 方法

## 測試最佳實踐

### 1. 測試隔離
- 每個測試使用獨立的資料庫
- 測試之間不互相影響

### 2. 測試資料
- 使用 fixtures 創建測試資料
- 測試後自動清理

### 3. 測試分類
- **單元測試**: 測試單一功能
- **整合測試**: 測試多個組件協作
- **端到端測試**: 測試完整流程

### 4. 持續改進
- 定期運行測試
- 新功能必須有對應測試
- 修復 bug 時添加回歸測試

## 下一步建議

1. **修復路由問題**: 檢查並修復 404 錯誤的路由
2. **增加測試覆蓋率**: 為更多功能添加測試
3. **性能測試**: 添加資料庫查詢性能測試
4. **安全測試**: 添加輸入驗證和安全性測試
5. **自動化**: 設置 CI/CD 自動運行測試