"""測試資料模型"""
import pytest
import tempfile
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from model import Base, User, AccountSubject, CompanyInfo, Account, PaymentIdentity, Department


@pytest.fixture
def test_db():
    """創建測試用的臨時資料庫"""
    # 創建臨時資料庫文件
    db_fd, db_path = tempfile.mkstemp()
    test_engine = create_engine(f'sqlite:///{db_path}')
    Base.metadata.create_all(test_engine)
    
    TestSession = sessionmaker(bind=test_engine)
    session = TestSession()
    
    yield session
    
    # 清理
    session.close()
    os.close(db_fd)
    os.unlink(db_path)


class TestUser:
    """測試 User 模型"""
    
    def test_create_user(self, test_db):
        """測試創建用戶"""
        user = User(username='testuser', email='<EMAIL>')
        test_db.add(user)
        test_db.commit()
        
        assert user.id is not None
        assert user.username == 'testuser'
        assert user.email == '<EMAIL>'
    
    def test_user_unique_constraints(self, test_db):
        """測試用戶唯一性約束"""
        user1 = User(username='testuser', email='<EMAIL>')
        user2 = User(username='testuser', email='<EMAIL>')
        
        test_db.add(user1)
        test_db.commit()
        
        test_db.add(user2)
        with pytest.raises(Exception):  # 應該拋出唯一性約束錯誤
            test_db.commit()


class TestAccountSubject:
    """測試會計科目模型"""
    
    def test_create_account_subject(self, test_db):
        """測試創建會計科目"""
        subject = AccountSubject(
            name='現金',
            code='1111',
            style='資產',
            top_category='資產',
            is_expandable=False,
            note='現金科目'
        )
        test_db.add(subject)
        test_db.commit()
        
        assert subject.id is not None
        assert subject.name == '現金'
        assert subject.code == '1111'
        assert subject.style == '資產'
        assert subject.top_category == '資產'
        assert subject.is_expandable is False
    
    def test_parent_child_relationship(self, test_db):
        """測試父子關係"""
        parent = AccountSubject(name='流動資產', code='11', style='資產', top_category='資產')
        child = AccountSubject(name='現金', code='1111', style='資產', top_category='資產')
        
        test_db.add(parent)
        test_db.commit()
        
        child.parent_id = parent.id
        test_db.add(child)
        test_db.commit()
        
        assert child.parent == parent
        assert child in parent.children


class TestCompanyInfo:
    """測試公司資訊模型"""
    
    def test_create_company_info(self, test_db):
        """測試創建公司資訊"""
        company = CompanyInfo(
            company_name='測試公司',
            company_id='********',
            owner_name='張三',
            owner_phone='**********',
            email='<EMAIL>',
            tax_office='國稅局',
            address='台北市信義區',
            contact_name='李四',
            contact_phone='**********',
            tax_id='********'
        )
        test_db.add(company)
        test_db.commit()
        
        assert company.id is not None
        assert company.company_name == '測試公司'
        assert company.company_id == '********'
        assert company.owner_name == '張三'


class TestAccount:
    """測試帳戶模型"""
    
    def test_create_cash_account(self, test_db):
        """測試創建現金帳戶"""
        account = Account(
            name='現金',
            category='現金',
            init_amount=100000,
            subject_code='1111',
            is_default=True,
            note='主要現金帳戶'
        )
        test_db.add(account)
        test_db.commit()
        
        assert account.id is not None
        assert account.name == '現金'
        assert account.category == '現金'
        assert account.init_amount == 100000
        assert account.is_default is True
    
    def test_create_bank_account(self, test_db):
        """測試創建銀行帳戶"""
        account = Account(
            name='台灣銀行支票戶',
            category='銀行帳戶',
            bank_name='台灣銀行',
            branch='信義分行',
            account_number='*********',
            account_holder='測試公司',
            init_amount=500000,
            subject_code='1112'
        )
        test_db.add(account)
        test_db.commit()
        
        assert account.bank_name == '台灣銀行'
        assert account.branch == '信義分行'
        assert account.account_number == '*********'


class TestPaymentIdentity:
    """測試付款身份模型"""
    
    def test_create_payment_identity(self, test_db):
        """測試創建付款身份"""
        identity = PaymentIdentity(
            type='客戶',
            name='ABC公司',
            tax_id='********',
            bank_code='012',
            bank_account='*********',
            contact='王五',
            mobile='**********',
            line='abc_company',
            note='重要客戶'
        )
        test_db.add(identity)
        test_db.commit()
        
        assert identity.id is not None
        assert identity.type == '客戶'
        assert identity.name == 'ABC公司'
        assert identity.tax_id == '********'


class TestDepartment:
    """測試部門模型"""
    
    def test_create_department(self, test_db):
        """測試創建部門"""
        dept = Department(
            name='資訊部',
            note='負責IT相關業務'
        )
        test_db.add(dept)
        test_db.commit()
        
        assert dept.id is not None
        assert dept.name == '資訊部'
        assert dept.note == '負責IT相關業務'
    
    def test_department_hierarchy(self, test_db):
        """測試部門階層關係"""
        parent_dept = Department(name='總公司')
        child_dept = Department(name='台北分公司')
        
        test_db.add(parent_dept)
        test_db.commit()
        
        child_dept.parent_id = parent_dept.id
        test_db.add(child_dept)
        test_db.commit()
        
        assert child_dept.parent == parent_dept
        assert child_dept in parent_dept.children