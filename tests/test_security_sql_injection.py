"""SQL 注入安全性測試"""
import pytest
import tempfile
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from model import Base, AccountSubject, Account, CompanyInfo, User
from app import create_app


@pytest.fixture
def security_db():
    """創建安全測試用的資料庫"""
    db_fd, db_path = tempfile.mkstemp()
    test_engine = create_engine(f'sqlite:///{db_path}')
    Base.metadata.create_all(test_engine)
    
    TestSession = sessionmaker(bind=test_engine)
    session = TestSession()
    
    # 創建測試資料
    test_subjects = [
        AccountSubject(name='現金', code='1111', style='資產', top_category='資產'),
        AccountSubject(name='銀行存款', code='1112', style='資產', top_category='資產'),
        AccountSubject(name='應收帳款', code='1113', style='資產', top_category='資產'),
    ]
    
    test_users = [
        User(username='admin', email='<EMAIL>'),
        User(username='user1', email='<EMAIL>'),
        User(username='user2', email='<EMAIL>'),
    ]
    
    session.add_all(test_subjects + test_users)
    session.commit()
    
    yield session, test_engine
    
    session.close()
    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture
def security_app():
    """創建安全測試用的應用"""
    app = create_app()
    app.config.update({
        'TESTING': True,
        'WTF_CSRF_ENABLED': False,
        'SECRET_KEY': 'test-secret-key'
    })
    return app


class TestSQLInjectionPrevention:
    """SQL 注入防護測試"""
    
    def test_orm_query_safety(self, security_db):
        """測試 ORM 查詢的安全性"""
        session, engine = security_db
        
        # 安全的 ORM 查詢 - 這些應該都是安全的
        safe_queries = [
            "1111",
            "admin",
            "<EMAIL>",
            "'; DROP TABLE account_subject; --",
            "1' OR '1'='1",
            "admin'; DELETE FROM users; --",
            "1111' UNION SELECT * FROM users --"
        ]
        
        for malicious_input in safe_queries:
            # 測試科目查詢
            result = session.query(AccountSubject).filter_by(code=malicious_input).first()
            # ORM 查詢應該安全處理這些輸入
            if malicious_input == "1111":
                assert result is not None
                assert result.name == '現金'
            else:
                assert result is None
            
            # 測試用戶查詢
            user_result = session.query(User).filter_by(username=malicious_input).first()
            if malicious_input == "admin":
                assert user_result is not None
                assert user_result.email == '<EMAIL>'
            else:
                assert user_result is None
    
    def test_parameterized_queries(self, security_db):
        """測試參數化查詢的安全性"""
        session, engine = security_db
        
        # 測試參數化查詢
        malicious_inputs = [
            "'; DROP TABLE account_subject; --",
            "1' OR '1'='1",
            "1111' UNION SELECT username, email FROM users --",
            "1111'; INSERT INTO users (username, email) VALUES ('hacker', '<EMAIL>'); --"
        ]
        
        for malicious_code in malicious_inputs:
            # 使用參數化查詢
            result = session.execute(
                text("SELECT * FROM account_subject WHERE code = :code"),
                {"code": malicious_code}
            ).fetchall()
            
            # 參數化查詢應該安全處理惡意輸入
            assert len(result) == 0, f"參數化查詢未正確處理惡意輸入: {malicious_code}"
    
    def test_dangerous_raw_sql_detection(self, security_db):
        """檢測危險的原始 SQL 使用"""
        session, engine = security_db
        
        malicious_input = "'; DROP TABLE users; --"
        
        # 這些是危險的 SQL 模式，應該避免
        dangerous_patterns = [
            ("SELECT * FROM users WHERE username = '{}'", [malicious_input]),
            ("DELETE FROM account_subject WHERE code = '{}'", [malicious_input]),
            ("INSERT INTO users VALUES ('{}', '{}')", [malicious_input, "<EMAIL>"]),
            ("UPDATE account_subject SET name = '{}' WHERE code = '{}'", [malicious_input, "1111"])
        ]
        
        for pattern, args in dangerous_patterns:
            # 模擬危險的字符串拼接 SQL
            dangerous_sql = pattern.format(*args)
            
            # 這種查詢應該被避免，我們測試它確實是危險的
            try:
                # 注意：這裡我們故意不執行這些危險查詢
                # 只是檢查它們包含危險模式
                assert "DROP TABLE" in dangerous_sql or "DELETE FROM" in dangerous_sql or \
                       "INSERT INTO" in dangerous_sql or "UPDATE" in dangerous_sql
                print(f"檢測到危險 SQL 模式: {dangerous_sql[:50]}...")
            except Exception as e:
                # 如果有異常，說明系統有某種保護機制
                print(f"SQL 執行被阻止: {str(e)[:50]}...")


class TestWebFormInjection:
    """Web 表單注入測試"""
    
    def test_subject_form_injection(self, security_app):
        """測試科目表單的注入防護"""
        client = security_app.test_client()
        
        # SQL 注入攻擊載荷
        injection_payloads = [
            "'; DROP TABLE account_subject; --",
            "1' OR '1'='1",
            "test'; DELETE FROM users; --",
            "'; INSERT INTO users VALUES ('hacker', '<EMAIL>'); --",
            "1111' UNION SELECT username, email FROM users --",
            "<script>alert('XSS')</script>",  # 也測試 XSS
            "'; EXEC xp_cmdshell('dir'); --"  # 命令注入
        ]
        
        for payload in injection_payloads:
            # 測試新增科目表單
            response = client.post('/accounting/add_subject', data={
                'sub_name': payload,
                'sub_code': payload,
                'sub_style': '資產',
                'sub_note': payload,
                'top_category': '資產'
            })
            
            # 應該正常處理（重定向或顯示表單），不應該出錯
            assert response.status_code in [200, 302, 400, 422], \
                f"表單注入測試失敗，載荷: {payload}"
            
            # 檢查響應中不應該包含敏感錯誤信息
            if response.status_code == 200:
                response_text = response.get_data(as_text=True)
                dangerous_keywords = ['error', 'exception', 'traceback', 'sql']
                for keyword in dangerous_keywords:
                    assert keyword.lower() not in response_text.lower(), \
                        f"響應包含敏感信息: {keyword}"
    
    def test_account_form_injection(self, security_app):
        """測試帳戶表單的注入防護"""
        client = security_app.test_client()
        
        injection_payloads = [
            "'; DROP TABLE account; --",
            "admin'; UPDATE users SET username='hacker' WHERE username='admin'; --",
            "test' OR '1'='1",
        ]
        
        for payload in injection_payloads:
            # 測試新增現金帳戶
            response = client.post('/account/add/cash', data={
                'name': payload,
                'init_amount': '1000',
                'subject_code': '001',
                'note': payload
            })
            
            assert response.status_code in [200, 302, 400, 422], \
                f"帳戶表單注入測試失敗，載荷: {payload}"
            
            # 測試新增銀行帳戶
            response = client.post('/account/add/bank', data={
                'name': payload,
                'bank_name': payload,
                'branch': payload,
                'account_number': payload,
                'account_holder': payload,
                'subject_code': '001',
                'note': payload
            })
            
            assert response.status_code in [200, 302, 400, 422], \
                f"銀行帳戶表單注入測試失敗，載荷: {payload}"


class TestQueryParameterInjection:
    """查詢參數注入測試"""
    
    def test_url_parameter_injection(self, security_app):
        """測試 URL 參數的注入防護"""
        client = security_app.test_client()
        
        # 測試編輯科目的 code 參數
        injection_payloads = [
            "1111'; DROP TABLE account_subject; --",
            "1111' OR '1'='1",
            "1111' UNION SELECT * FROM users --",
            "../../../etc/passwd",  # 路徑遍歷
            "%27%20OR%20%271%27%3D%271",  # URL 編碼的注入
        ]
        
        for payload in injection_payloads:
            # 測試 GET 參數注入
            response = client.get(f'/accounting/edit_subject?code={payload}')
            
            # 應該返回 404 或正常頁面，不應該出錯
            assert response.status_code in [200, 404, 400], \
                f"URL 參數注入測試失敗，載荷: {payload}"
            
            if response.status_code == 200:
                response_text = response.get_data(as_text=True)
                # 不應該洩露敏感信息
                assert 'error' not in response_text.lower()
                assert 'exception' not in response_text.lower()


class TestSecurityHeaders:
    """安全標頭測試"""
    
    def test_security_headers_present(self, security_app):
        """測試安全標頭是否存在"""
        client = security_app.test_client()
        
        response = client.get('/')
        
        # 檢查重要的安全標頭
        headers_to_check = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection',
        ]
        
        for header in headers_to_check:
            # 注意：這些標頭可能需要在應用中配置
            if header in response.headers:
                print(f"✅ 安全標頭存在: {header} = {response.headers[header]}")
            else:
                print(f"⚠️  建議添加安全標頭: {header}")


class TestInputValidation:
    """輸入驗證測試"""
    
    def test_numeric_field_validation(self, security_app):
        """測試數值欄位的驗證"""
        client = security_app.test_client()
        
        # 測試金額欄位的注入
        malicious_amounts = [
            "'; DROP TABLE account; --",
            "1000; DELETE FROM users; --",
            "1000' OR '1'='1",
            "<script>alert('XSS')</script>",
            "../../etc/passwd",
        ]
        
        for amount in malicious_amounts:
            response = client.post('/account/add/cash', data={
                'name': '測試帳戶',
                'init_amount': amount,  # 惡意金額
                'subject_code': '001',
                'note': '測試'
            })
            
            # 應該正常處理或返回驗證錯誤
            assert response.status_code in [200, 302, 400, 422], \
                f"數值驗證測試失敗，載荷: {amount}"
    
    def test_code_field_validation(self, security_app):
        """測試代碼欄位的驗證"""
        client = security_app.test_client()
        
        malicious_codes = [
            "'; DROP TABLE account_subject; --",
            "1111' OR '1'='1",
            "../../../etc/passwd",
            "<script>alert('XSS')</script>",
            "1111'; INSERT INTO users VALUES ('hacker', '<EMAIL>'); --"
        ]
        
        for code in malicious_codes:
            response = client.post('/accounting/add_subject', data={
                'sub_name': '測試科目',
                'sub_code': code,  # 惡意代碼
                'sub_style': '資產',
                'top_category': '資產'
            })
            
            assert response.status_code in [200, 302, 400, 422], \
                f"代碼驗證測試失敗，載荷: {code}"


class TestDatabaseIntegrityAfterAttacks:
    """攻擊後資料庫完整性測試"""
    
    def test_database_integrity_after_injection_attempts(self, security_db):
        """測試注入攻擊後資料庫的完整性"""
        session, engine = security_db
        
        # 記錄攻擊前的資料
        initial_subjects = session.query(AccountSubject).count()
        initial_users = session.query(User).count()
        
        # 嘗試各種注入攻擊
        injection_attempts = [
            "'; DROP TABLE account_subject; --",
            "'; DELETE FROM users; --",
            "'; INSERT INTO users VALUES ('hacker', '<EMAIL>'); --",
            "1111' UNION SELECT * FROM users --"
        ]
        
        for attack in injection_attempts:
            try:
                # 嘗試通過 ORM 進行攻擊（應該是安全的）
                session.query(AccountSubject).filter_by(code=attack).first()
                session.query(User).filter_by(username=attack).first()
                
                # 嘗試參數化查詢攻擊（應該是安全的）
                session.execute(
                    text("SELECT * FROM account_subject WHERE code = :code"),
                    {"code": attack}
                ).fetchall()
                
            except Exception as e:
                # 如果有異常，記錄但繼續測試
                print(f"攻擊被阻止: {attack} -> {str(e)[:50]}...")
        
        # 檢查資料庫完整性
        final_subjects = session.query(AccountSubject).count()
        final_users = session.query(User).count()
        
        assert final_subjects == initial_subjects, "科目表資料被破壞"
        assert final_users == initial_users, "用戶表資料被破壞"
        
        # 檢查測試資料是否仍然存在
        cash_subject = session.query(AccountSubject).filter_by(code='1111').first()
        assert cash_subject is not None, "測試資料遺失"
        assert cash_subject.name == '現金', "測試資料被修改"
        
        admin_user = session.query(User).filter_by(username='admin').first()
        assert admin_user is not None, "管理員用戶遺失"
        assert admin_user.email == '<EMAIL>', "管理員資料被修改"