"""pytest 配置文件 - 共享的 fixtures"""
import pytest
import tempfile
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from model import Base, AccountSubject, CompanyInfo, Account
from main import create_app


@pytest.fixture(scope="session")
def test_database():
    """會話級別的測試資料庫"""
    db_fd, db_path = tempfile.mkstemp()
    test_engine = create_engine(f'sqlite:///{db_path}')
    Base.metadata.create_all(test_engine)
    
    yield test_engine, db_path
    
    # 清理
    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture
def db_session(test_database):
    """每個測試的資料庫會話"""
    engine, db_path = test_database
    TestSession = sessionmaker(bind=engine)
    session = TestSession()
    
    yield session
    
    session.rollback()
    session.close()


@pytest.fixture
def sample_account_subjects(db_session):
    """創建範例會計科目資料"""
    subjects = [
        AccountSubject(name='資產', code='1', style='資產', top_category='資產'),
        AccountSubject(name='流動資產', code='11', style='資產', top_category='資產'),
        AccountSubject(name='現金', code='1111', style='資產', top_category='資產'),
        AccountSubject(name='銀行存款', code='1112', style='資產', top_category='資產'),
        AccountSubject(name='負債', code='2', style='負債', top_category='負債'),
        AccountSubject(name='流動負債', code='21', style='負債', top_category='負債'),
    ]
    
    for subject in subjects:
        db_session.add(subject)
    db_session.commit()
    
    # 設定父子關係
    asset_main = db_session.query(AccountSubject).filter_by(code='1').first()
    current_asset = db_session.query(AccountSubject).filter_by(code='11').first()
    cash = db_session.query(AccountSubject).filter_by(code='1111').first()
    bank = db_session.query(AccountSubject).filter_by(code='1112').first()
    
    current_asset.parent_id = asset_main.id
    cash.parent_id = current_asset.id
    bank.parent_id = current_asset.id
    
    liability_main = db_session.query(AccountSubject).filter_by(code='2').first()
    current_liability = db_session.query(AccountSubject).filter_by(code='21').first()
    current_liability.parent_id = liability_main.id
    
    db_session.commit()
    
    return subjects


@pytest.fixture
def sample_company_info(db_session):
    """創建範例公司資訊"""
    company = CompanyInfo(
        company_name='測試股份有限公司',
        company_id='********',
        owner_name='張三',
        owner_phone='09********',
        email='<EMAIL>',
        tax_office='台北國稅局',
        address='台北市信義區信義路五段7號',
        contact_name='李四',
        contact_phone='0*********',
        tax_id='********'
    )
    
    db_session.add(company)
    db_session.commit()
    
    return company


@pytest.fixture
def sample_accounts(db_session):
    """創建範例帳戶資料"""
    accounts = [
        Account(
            name='現金',
            category='現金',
            init_amount=100000,
            subject_code='1111',
            is_default=True,
            note='主要現金帳戶'
        ),
        Account(
            name='台灣銀行支票戶',
            category='銀行帳戶',
            bank_name='台灣銀行',
            branch='信義分行',
            account_number='*********',
            account_holder='測試股份有限公司',
            init_amount=500000,
            subject_code='1112',
            note='主要銀行帳戶'
        ),
        Account(
            name='第一銀行活期存款',
            category='銀行帳戶',
            bank_name='第一銀行',
            branch='台北分行',
            account_number='*********',
            account_holder='測試股份有限公司',
            init_amount=200000,
            subject_code='1112'
        )
    ]
    
    for account in accounts:
        db_session.add(account)
    db_session.commit()
    
    return accounts


@pytest.fixture
def app_with_test_config():
    """創建測試配置的 Flask 應用"""
    app = create_app()
    app.config.update({
        'TESTING': True,
        'WTF_CSRF_ENABLED': False,
        'SECRET_KEY': 'test-secret-key'
    })
    
    return app


@pytest.fixture
def client(app_with_test_config):
    """Flask 測試客戶端"""
    return app_with_test_config.test_client()


@pytest.fixture
def authenticated_client(client):
    """已認證的測試客戶端（如果有認證系統的話）"""
    # 如果有登入系統，可以在這裡模擬登入
    # client.post('/login', data={'username': 'test', 'password': 'test'})
    return client