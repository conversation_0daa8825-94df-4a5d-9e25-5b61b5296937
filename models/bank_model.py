"""銀行資料模型"""
from sqlalchemy import Column, Integer, String, ForeignKey, Boolean, Text
from sqlalchemy.orm import relationship
from model import Base


class Bank(Base):
    """銀行主表"""
    __tablename__ = 'banks'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(10), unique=True, nullable=False, index=True)  # 銀行代碼 (如: 004)
    name = Column(String(100), nullable=False, index=True)              # 銀行名稱 (如: 台灣銀行)
    full_name = Column(String(200))                                     # 完整名稱
    english_name = Column(String(200))                                  # 英文名稱
    phone = Column(String(20))                                          # 客服電話
    website = Column(String(200))                                       # 官方網站
    is_active = Column(Boolean, default=True, index=True)               # 是否營業中
    
    # 關聯到分行
    branches = relationship("BankBranch", back_populates="bank", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Bank(code='{self.code}', name='{self.name}')>"


class BankBranch(Base):
    """銀行分行表"""
    __tablename__ = 'bank_branches'
    
    id = Column(Integer, primary_key=True)
    bank_id = Column(Integer, ForeignKey('banks.id'), nullable=False, index=True)
    code = Column(String(20), nullable=False, index=True)               # 分行代碼
    name = Column(String(100), nullable=False, index=True)              # 分行名稱
    address = Column(Text)                                              # 地址
    phone = Column(String(20))                                          # 電話
    fax = Column(String(20))                                            # 傳真
    
    # 地理位置
    city = Column(String(20), index=True)                               # 城市
    district = Column(String(20), index=True)                           # 區域
    
    # 服務資訊
    has_atm = Column(Boolean, default=True)                             # 是否有ATM
    has_counter = Column(Boolean, default=True)                         # 是否有櫃檯
    is_active = Column(Boolean, default=True, index=True)               # 是否營業中
    
    # 營業時間 (可以用JSON格式儲存)
    business_hours = Column(Text)                                       # 營業時間JSON
    
    # 關聯到銀行
    bank = relationship("Bank", back_populates="branches")
    
    def __repr__(self):
        return f"<BankBranch(code='{self.code}', name='{self.name}')>"


class BankService(Base):
    """銀行服務表 (可選)"""
    __tablename__ = 'bank_services'
    
    id = Column(Integer, primary_key=True)
    bank_id = Column(Integer, ForeignKey('banks.id'), nullable=False, index=True)
    service_type = Column(String(50), nullable=False, index=True)       # 服務類型
    service_name = Column(String(100), nullable=False)                  # 服務名稱
    description = Column(Text)                                          # 服務描述
    is_available = Column(Boolean, default=True)                        # 是否可用
    
    # 添加關聯
    bank = relationship("Bank", backref="services")
    
    def __repr__(self):
        return f"<BankService(service_type='{self.service_type}', name='{self.service_name}')>"