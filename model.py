from sqlalchemy import Column, Inte<PERSON>, String, <PERSON><PERSON><PERSON>, Text, ForeignKey, create_engine, DateTime, Date, Index, Float
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.orm import sessionmaker
import os 
import sys
from datetime import datetime, timezone, timedelta

# 定義台灣時間函數
def get_taiwan_time():
    """獲取台灣時間（精確到秒，不含時區顯示）"""
    now = datetime.now(timezone(timedelta(hours=8)))
    # 去掉微秒和時區信息，只保留到秒
    return now.replace(microsecond=0, tzinfo=None)

Base = declarative_base()

# ============================================================================
# 資料表定義 (按字母順序排列)
# ============================================================================

class Account(Base):
    """帳戶資料表"""
    __tablename__ = 'account'
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, index=True)              #帳戶名稱
    category = Column(String(20), nullable=False, index=True)           #帳戶類別（現金/銀行帳戶）
    note = Column(Text)                                                 #備註
    # 銀行帳戶專屬欄位
    bank_name = Column(String(100))                                     #銀行代號
    branch = Column(String(100))                                        #分行代號
    account_number = Column(String(50), index=True)                     #帳號
    account_holder = Column(String(100))                                #戶名
    # 新增欄位
    init_amount = Column(Integer)                                       #期初金額，只允許整數
    subject_code = Column(String(20), ForeignKey('account_subject.code'), index=True)  # 添加外鍵關係和索引
    is_default = Column(Boolean, default=False, index=True)             #是否預設帳戶
    cover_image = Column(String(200))                                   #存摺封面檔名或路徑
    
    # 添加關聯
    subject = relationship('AccountSubject', backref='accounts')

class AccountSubject(Base):
    """科目資料表"""
    __tablename__ = 'account_subject'
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)                          #科目名稱
    code = Column(String(20), nullable=False, unique=True, index=True)  #科目代碼
    parent_id = Column(Integer, ForeignKey('account_subject.id'), index=True)  # 主分類id
    is_expandable = Column(Boolean, default=True, index=True)           #是否可抵扣
    note = Column(Text)                                                 #備註
    top_category = Column(String(50), index=True)                       # 最上層分類（如資產、負債...）
    
    parent = relationship('AccountSubject', remote_side=[id], backref='children')

class BankBranch(Base):
    """銀行分行資料表"""
    __tablename__ = 'bank_branches'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False, comment='分行代碼')
    name = Column(String(100), nullable=False, comment='分行名稱')
    head_office_code = Column(String(10), ForeignKey('bank_head_offices.code'), nullable=False, comment='總行代碼', index=True)
    created_at = Column(DateTime, default=get_taiwan_time)
    
    # 關聯到總行
    head_office = relationship("BankHeadOffice", back_populates="branches")

class BankHeadOffice(Base):
    """銀行總行資料表"""
    __tablename__ = 'bank_head_offices'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(10), unique=True, nullable=False, comment='銀行代碼')
    name = Column(String(100), nullable=False, comment='銀行名稱')
    created_at = Column(DateTime, default=get_taiwan_time)
    
    # 關聯到分行
    branches = relationship("BankBranch", back_populates="head_office")

class CompanyInfo(Base):
    """用戶的公司資料表"""
    __tablename__ = 'company_info'
    id = Column(Integer, primary_key=True)
    company_name = Column(String(200), nullable=False)                  #公司名稱
    company_id = Column(String(20))                                     #公司統編
    owner_name = Column(String(100), nullable=False)                    #負責人姓名
    owner_phone = Column(String(20), nullable=False)                    #負責人聯絡電話
    email = Column(String(200), nullable=False)
    tax_office = Column(String(200))                                    #稅徵機關名稱
    address = Column(String(500))                                       #營業地址
    contact_name = Column(String(100))                                  #扣繳申報聯絡人姓名
    contact_phone = Column(String(20))                                  #扣繳申報聯絡人電話
    tax_id = Column(String(20))                                         #稅籍編號

class Department(Base):
    """部門資料表"""
    __tablename__ = 'department'
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, index=True)              #部門名稱
    parent_id = Column(Integer, ForeignKey('department.id'), index=True)  # 上層部門 id
    note = Column(String(200))                                          #備註

    parent = relationship('Department', remote_side=[id], backref='children')

class Money(Base):
    """收支紀錄資料表"""
    __tablename__ = 'money'
    id = Column(Integer, primary_key=True)
    money_type = Column(String(20), comment='收支類型', index=True)
    a_time = Column(Date, comment='記帳時間', index=True)
    name = Column(String(100), comment='名稱')
    total = Column(Integer, comment='總計(含稅)')
    extra_fee = Column(Integer, comment='手續費')
    subject_code = Column(String(20), ForeignKey('account_subject.code'), comment='科目代碼', index=True)
    account_id = Column(Integer, ForeignKey('account.id'), comment='帳戶', index=True)
    payment_identity_id = Column(Integer, ForeignKey('payment_identity.id'), comment='收支對象', index=True)
    is_paper = Column(Boolean, default=False, comment='是否非發票')
    number = Column(String(20), nullable=True, comment='發票號碼')
    tax_type = Column(String(20), comment='稅別')
    buyer_tax_id = Column(String(20), comment='買方統編')
    seller_tax_id = Column(String(20), comment='賣方統編')
    date = Column(String(20), comment='發票日期')
    is_paid = Column(Boolean, default=False, comment='是否已收款', index=True)
    should_paid_date = Column(DateTime, comment='應收款日期', index=True)
    paid_date = Column(DateTime, comment='實收付日期', index=True)
    note = Column(Text, comment='備註')
    created_at = Column(DateTime, default=get_taiwan_time, index=True)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time)
    # 新增欄位
    department_id = Column(Integer, ForeignKey('department.id'), comment='部門別', index=True)
    project_id = Column(Integer, ForeignKey('project.id'), comment='專案別', index=True)
    tags = Column(String(200), comment='標籤')
    image_path = Column(String(300), comment='圖片位置')
    
    # 關聯
    subject = relationship('AccountSubject', backref='money_records')
    account = relationship('Account', backref='money_records')
    payment_identity = relationship('PaymentIdentity', backref='money_records')
    department = relationship('Department', backref='money_records')
    project = relationship('Project', backref='money_records')
    
    # 複合索引 - 優化常用查詢
    __table_args__ = (
        Index('ix_money_date_type', 'a_time', 'money_type'),
        Index('ix_money_account_date', 'account_id', 'a_time'),
        Index('ix_money_subject_date', 'subject_code', 'a_time'),
        Index('ix_money_dept_project', 'department_id', 'project_id'),
    )

class PaymentIdentity(Base):
    """收支對象資料表"""
    __tablename__ = 'payment_identity'
    id = Column(Integer, primary_key=True)
    type = Column(String(20), index=True)                               #客戶類型(供應商，客戶，其他)
    name = Column(String(100), nullable=False, index=True)              # 公司名稱
    tax_id = Column(String(20), index=True)                             #公司統編
    bank_code = Column(String(20))                                      #銀行代碼
    bank_account = Column(String(50))                                   #銀行帳號
    contact = Column(String(50))                                        #聯絡人姓名
    mobile = Column(String(50))                                         #手機
    line = Column(String(100))                                          #Line ID
    note = Column(String(200))                                          #備註

class Project(Base):
    """專案資料表"""
    __tablename__ = 'project'
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, index=True)              #專案名稱
    code = Column(String(20), unique=True, nullable=False, index=True)  # 專案代碼
    description = Column(Text)                                          #專案描述
    start_date = Column(DateTime)                                       #開始日期
    end_date = Column(DateTime)                                         #結束日期
    status = Column(String(20), default='進行中', index=True)           #專案狀態（進行中、已完成、暫停）
    budget = Column(Integer)                                            #預算金額
    department_id = Column(Integer, ForeignKey('department.id'), index=True)  #負責部門
    manager = Column(String(100))                                       #專案負責人
    note = Column(String(200))                                          #備註
    created_at = Column(DateTime, default=get_taiwan_time)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time)

    # 關聯到部門
    department = relationship('Department', backref='projects')

class ShareAccount(Base):
    """分享帳簿資料表"""
    __tablename__ = 'share_account'
    id = Column(Integer, primary_key=True)
    date = Column(String(20), nullable=False)                           # 日期
    type = Column(String(20), nullable=False)                           # 類型
    range = Column(String(50))                                          # 範圍
    user = Column(String(100), nullable=False)                          # 使用者
    query_type = Column(String(20), nullable=False)                     # 查詢類型
    start_date = Column(String(20))                                     #開始日期
    end_date = Column(String(20))                                       #結束日期
    expired = Column(Boolean, default=False)                           #是否過期

class Employee(Base):
    """員工資料表"""
    __tablename__ = 'employees'

    id = Column(Integer, primary_key=True)

    # 基本資料
    title = Column(String(100), comment='職稱')
    emp_id = Column(String(50), unique=True, comment='員工編號', index=True)
    name = Column(String(100), nullable=False, comment='姓名', index=True)
    identity = Column(String(20), unique=True, comment='身份證號', index=True)
    onboard_date = Column(Date, comment='到職日', index=True)
    leave_date = Column(Date, comment='離職日', index=True)
    department_name = Column(String(100), comment='部門')
    address = Column(String(500), comment='通訊地址')
    phone = Column(String(20), comment='聯絡電話')
    email = Column(String(200), comment='電子信箱')

    # 薪資資訊
    salary = Column(Integer, default=0, comment='本薪')
    meal = Column(Integer, default=0, comment='伙食費')
    bank = Column(String(100), comment='薪資匯款銀行')
    bank_account = Column(String(50), comment='薪資匯款帳號')

    # 保險身份
    insurance_identity = Column(String(20), comment='保險身份')  # 負責人/勞工
    labor_insurance = Column(String(10), comment='是否投保勞保、勞退')  # yes/no
    health_insurance = Column(String(10), comment='是否投保健保')  # yes/no

    # 健康保險相關
    health_insurance_date = Column(Date, comment='健保加保日')
    health_subsidy_qualification = Column(String(20), comment='本人健保補助資格')
    health_law_effective_date = Column(Date, comment='健保級距法規生效日')
    health_level = Column(String(10), comment='健保投保級距')

    # 健保眷屬
    dependents_none = Column(Integer, default=0, comment='健保眷屬-無補助')
    dependents_1_4 = Column(Integer, default=0, comment='健保眷屬-補助1/4')
    dependents_1_2 = Column(Integer, default=0, comment='健保眷屬-補助1/2')
    dependents_local = Column(Integer, default=0, comment='健保眷屬-補助地區人口保費')
    dependents_full = Column(Integer, default=0, comment='健保眷屬-補助全額')

    # 勞工保險相關
    labor_insurance_date = Column(Date, comment='勞保加保日')
    labor_law_effective_date = Column(Date, comment='勞保級距法規生效日')
    labor_level = Column(String(10), comment='勞保投保級距')
    labor_insurance_items = Column(String(200), comment='勞保投保項目')  # 存儲多選項目，用逗號分隔

    # 職業災害保險相關
    occupational_law_effective_date = Column(Date, comment='職保級距法規生效日')
    occupational_level = Column(String(10), comment='職保投保級距')

    # 系統欄位
    created_at = Column(DateTime, default=get_taiwan_time, comment='建立時間', index=True)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time, comment='更新時間')
    is_active = Column(Boolean, default=True, comment='是否啟用', index=True)

class SalarySetting(Base):
    """薪資設定資料表"""
    __tablename__ = 'salary_settings'

    id = Column(Integer, primary_key=True)
    payday = Column(Integer, default=10, comment='發薪日（每月幾號）')
    fund_account_id = Column(Integer, ForeignKey('account.id'), comment='資金帳戶ID', index=True)
    days_type = Column(String(20), default='calendar', comment='計算基準（calendar=日曆天數，fixed=固定30天）')

    # 系統欄位
    created_at = Column(DateTime, default=get_taiwan_time, comment='建立時間')
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time, comment='更新時間')

    # 關聯
    fund_account = relationship('Account', backref='salary_settings')

class User(Base):
    """使用者資料表"""
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True)
    username = Column(String(64), unique=True, nullable=False)
    email = Column(String(120), unique=True, nullable=False)

class Transfer(Base):
    __tablename__ = 'transfers'
    id = Column(Integer, primary_key=True)
    out_account_id = Column(Integer, ForeignKey('account.id'))
    in_account_id = Column(Integer, ForeignKey('account.id'))
    subject_code = Column(String(50))
    amount = Column(Float)
    fee = Column(Float)
    note = Column(Text)
    transfer_date = Column(Date)
    voucher = Column(String(255))  # 憑證圖檔路徑

    out_account = relationship("Account", foreign_keys=[out_account_id])
    in_account = relationship("Account", foreign_keys=[in_account_id])

# ============================================================================
# 資料庫設定
# ============================================================================

# 獲取可執行文件的路徑（包括 exe 或 py）
executable_path = sys.executable

# 如果是打包成 exe 後的可執行文件，則獲取該文件所在目錄
if getattr(sys, 'frozen', False):
    executable_dir = os.path.dirname(executable_path)
else:
    # 如果是直接運行 .py 文件，則獲取該文件所在目錄
    executable_dir = os.path.dirname(os.path.abspath(__file__))

# 使用相對路徑構建數據庫連接URI
db_name = 'app.db'
db_path = os.path.join(executable_dir, db_name)
DATABASE_URI = f"sqlite:///{db_path}"

# 建立資料庫引擎（這裡用 SQLite 範例）
engine = create_engine(DATABASE_URI)

# 建立所有資料表
Base.metadata.create_all(engine)

# 建立 session 工廠
Session = sessionmaker(bind=engine)
session = Session()
