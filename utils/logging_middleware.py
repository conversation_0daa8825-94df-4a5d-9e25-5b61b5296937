"""日誌中間件"""
import time
import logging
from flask import request, g
from config.logging_config import LoggingConfig, SecurityLogger, PerformanceLogger


class LoggingMiddleware:
    """日誌中間件類"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化應用"""
        app.before_request(self.before_request)
        app.after_request(self.after_request)
        app.teardown_appcontext(self.teardown)
    
    def before_request(self):
        """請求前處理"""
        g.start_time = time.time()
        g.request_id = self._generate_request_id()
        
        # 記錄請求開始
        app_logger = logging.getLogger('accounting')
        app_logger.info(f"[{g.request_id}] 請求開始: {request.method} {request.url}")
        
        # 檢查可疑輸入
        self._check_suspicious_input()
    
    def after_request(self, response):
        """請求後處理"""
        if hasattr(g, 'start_time'):
            response_time = (time.time() - g.start_time) * 1000
            
            # 記錄訪問日誌
            LoggingConfig.log_request(
                self.app, request, response, response_time
            )
            
            # 記錄慢請求
            if response_time > 2000:  # 超過2秒
                PerformanceLogger.log_slow_request(
                    request.endpoint or request.url,
                    request.method,
                    response_time,
                    request.remote_addr
                )
            
            # 記錄請求完成
            app_logger = logging.getLogger('accounting')
            app_logger.info(f"[{getattr(g, 'request_id', 'unknown')}] 請求完成: {response.status_code} - {response_time:.2f}ms")
        
        return response
    
    def teardown(self, exception):
        """請求結束處理"""
        if exception:
            error_logger = logging.getLogger('accounting')
            error_logger.error(f"請求異常: {str(exception)}", exc_info=True)
    
    def _generate_request_id(self):
        """生成請求ID"""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def _check_suspicious_input(self):
        """檢查可疑輸入"""
        user_ip = request.remote_addr or 'unknown'
        
        # 檢查查詢參數
        for key, value in request.args.items():
            if SecurityLogger.log_suspicious_input(value, f'query_param:{key}', user_ip):
                break
        
        # 檢查表單數據
        if request.form:
            for key, value in request.form.items():
                if SecurityLogger.log_suspicious_input(value, f'form_data:{key}', user_ip):
                    break
        
        # 檢查JSON數據
        if request.is_json and request.json:
            json_str = str(request.json)
            SecurityLogger.log_suspicious_input(json_str, 'json_data', user_ip)


class DatabaseLogger:
    """資料庫日誌記錄器"""
    
    @staticmethod
    def log_query(query, params=None, duration=None):
        """記錄資料庫查詢"""
        db_logger = logging.getLogger('accounting.database')
        
        if duration and duration > 1000:  # 超過1秒的查詢
            PerformanceLogger.log_slow_query(str(query), duration, params)
        
        # 記錄所有查詢（調試模式）
        if db_logger.isEnabledFor(logging.DEBUG):
            db_logger.debug(f"SQL查詢: {query}, 參數: {params}, 耗時: {duration}ms")
    
    @staticmethod
    def log_transaction(operation, table, record_id=None, duration=None):
        """記錄資料庫事務"""
        db_logger = logging.getLogger('accounting.database')
        
        message = f"資料庫操作: {operation} on {table}"
        if record_id:
            message += f" (ID: {record_id})"
        if duration:
            message += f" - 耗時: {duration:.2f}ms"
        
        db_logger.info(message)


class BusinessLogger:
    """業務日誌記錄器"""
    
    @staticmethod
    def log_business_event(event_type, details, user_id=None, severity='INFO'):
        """記錄業務事件"""
        business_logger = logging.getLogger('accounting.business')
        
        message = f"[{event_type}]"
        if user_id:
            message += f" 用戶ID: {user_id}"
        message += f" - {details}"
        
        if severity == 'ERROR':
            business_logger.error(message)
        elif severity == 'WARNING':
            business_logger.warning(message)
        else:
            business_logger.info(message)
    
    @staticmethod
    def log_account_operation(operation, account_code, amount=None, user_id=None):
        """記錄會計操作"""
        details = f"科目: {account_code}"
        if amount is not None:
            details += f", 金額: {amount}"
        
        BusinessLogger.log_business_event(
            f'ACCOUNT_{operation.upper()}',
            details,
            user_id
        )
    
    @staticmethod
    def log_financial_transaction(transaction_type, amount, from_account=None, to_account=None, user_id=None):
        """記錄財務交易"""
        details = f"類型: {transaction_type}, 金額: {amount}"
        if from_account:
            details += f", 來源: {from_account}"
        if to_account:
            details += f", 目標: {to_account}"
        
        BusinessLogger.log_business_event(
            'FINANCIAL_TRANSACTION',
            details,
            user_id
        )


# 日誌工具函數
def get_logger(name):
    """獲取指定名稱的日誌記錄器"""
    return logging.getLogger(f'accounting.{name}')


def log_user_action(action, details='', user_id=None):
    """記錄用戶操作"""
    user_logger = get_logger('user_action')
    
    message = f"用戶操作: {action}"
    if user_id:
        message += f" (用戶ID: {user_id})"
    if details:
        message += f" - {details}"
    
    user_logger.info(message)


def log_system_event(event, details='', severity='INFO'):
    """記錄系統事件"""
    system_logger = get_logger('system')
    
    message = f"系統事件: {event}"
    if details:
        message += f" - {details}"
    
    if severity == 'ERROR':
        system_logger.error(message)
    elif severity == 'WARNING':
        system_logger.warning(message)
    else:
        system_logger.info(message)