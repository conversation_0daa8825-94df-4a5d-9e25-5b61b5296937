from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from contextlib import contextmanager
from config.config import Config

# 建立資料庫引擎
# 建立資料庫引擎（優化版）
engine = create_engine(
    Config.get_database_uri(),
    pool_size=20,          # 增加連接池大小
    max_overflow=30,       # 增加溢出連接數
    pool_timeout=30,       # 連接超時時間
    pool_recycle=3600,     # 連接回收時間（1小時）
    echo=False             # 生產環境關閉 SQL 日誌
)

# 建立 session 工廠
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 建立 scoped session
db_session = scoped_session(SessionLocal)

@contextmanager
def get_db():
    """資料庫連線的 context manager"""
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()

def init_db():
    """初始化資料庫"""
    from model import Base
    Base.metadata.create_all(engine) 