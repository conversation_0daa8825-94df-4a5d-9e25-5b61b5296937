#!/usr/bin/env python3
"""
測試員工管理系統
"""

from model import Employee, session
from datetime import date

def test_employee_crud():
    """測試員工的增刪改查功能"""
    
    print("🧪 開始測試員工管理系統...")
    
    # 1. 測試新增員工
    print("\n1. 測試新增員工...")
    test_employee = Employee(
        title="測試工程師",
        emp_id="TEST001",
        name="測試員工",
        identity="A*********",
        onboard_date=date.today(),
        department_name="測試部門",
        address="台北市信義區",
        phone="**********",
        email="<EMAIL>",
        salary=50000,
        meal=3000,
        bank="台灣銀行",
        bank_account="*********",
        insurance_identity="勞工",
        labor_insurance="yes",
        health_insurance="yes",
        health_subsidy_qualification="無",
        health_level="1",
        labor_level="1",
        labor_insurance_items="普通事故保險,就業保險"
    )
    
    try:
        session.add(test_employee)
        session.commit()
        print("✅ 員工新增成功")
    except Exception as e:
        session.rollback()
        print(f"❌ 員工新增失敗：{e}")
        return
    
    # 2. 測試查詢員工
    print("\n2. 測試查詢員工...")
    try:
        employees = session.query(Employee).filter(Employee.is_active == True).all()
        print(f"✅ 查詢成功，共找到 {len(employees)} 位員工")
        for emp in employees:
            print(f"   - {emp.name} ({emp.emp_id})")
    except Exception as e:
        print(f"❌ 查詢失敗：{e}")
    
    # 3. 測試更新員工
    print("\n3. 測試更新員工...")
    try:
        employee = session.query(Employee).filter(Employee.emp_id == "TEST001").first()
        if employee:
            employee.title = "資深測試工程師"
            employee.salary = 60000
            session.commit()
            print("✅ 員工更新成功")
        else:
            print("❌ 找不到要更新的員工")
    except Exception as e:
        session.rollback()
        print(f"❌ 員工更新失敗：{e}")
    
    # 4. 測試軟刪除員工
    print("\n4. 測試軟刪除員工...")
    try:
        employee = session.query(Employee).filter(Employee.emp_id == "TEST001").first()
        if employee:
            employee.is_active = False
            session.commit()
            print("✅ 員工軟刪除成功")
        else:
            print("❌ 找不到要刪除的員工")
    except Exception as e:
        session.rollback()
        print(f"❌ 員工軟刪除失敗：{e}")
    
    # 5. 驗證軟刪除
    print("\n5. 驗證軟刪除...")
    try:
        active_employees = session.query(Employee).filter(Employee.is_active == True).all()
        all_employees = session.query(Employee).all()
        print(f"✅ 驗證成功 - 啟用員工：{len(active_employees)}，總員工：{len(all_employees)}")
    except Exception as e:
        print(f"❌ 驗證失敗：{e}")
    
    print("\n🎉 員工管理系統測試完成！")

if __name__ == "__main__":
    test_employee_crud()
