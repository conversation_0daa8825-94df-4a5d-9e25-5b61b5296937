from flask import Flask, render_template, jsonify, request, g, redirect
import sys
import os
import logging
from logging.handlers import RotatingFileHandler
import time

# 添加配置和數據路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'data'))

from config.config import Config
from database import init_db

# 直接在這裡定義性能監控功能
from datetime import datetime, timezone, timedelta
from collections import defaultdict, deque
from routes.main import main_bp
from routes.income_expense import income_expense_bp
from routes.assets import assets_bp
from routes.payroll import payroll_bp
from routes.service_reward import service_reward_bp
from routes.settings import settings_bp
from routes.accounting_other import accounting_other_bp
from routes.accounting import accounting_bp
from routes.account import account_bp
from routes.monitoring import monitoring_bp
from routes.share_account import share_account_bp

class SimplePerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'request_times': deque(maxlen=1000),
            'slow_requests': deque(maxlen=100),
            'error_count': defaultdict(int),
            'endpoint_stats': defaultdict(lambda: {
                'count': 0, 'total_time': 0, 'avg_time': 0, 'max_time': 0
            })
        }
        self.start_time = time.time()
    
    def record_request(self, endpoint, method, response_time, status_code):
        now = datetime.now(timezone(timedelta(hours=8))).replace(microsecond=0, tzinfo=None)
        self.metrics['request_times'].append({
            'time': now, 'response_time': response_time,
            'endpoint': endpoint, 'method': method, 'status_code': status_code
        })
        
        key = f"{method} {endpoint}"
        stats = self.metrics['endpoint_stats'][key]
        stats['count'] += 1
        stats['total_time'] += response_time
        stats['avg_time'] = stats['total_time'] / stats['count']
        stats['max_time'] = max(stats['max_time'], response_time)
        
        if response_time > 2000:
            self.metrics['slow_requests'].append({
                'time': now, 'endpoint': endpoint, 'method': method,
                'response_time': response_time, 'status_code': status_code
            })
        
        if status_code >= 400:
            self.metrics['error_count'][status_code] += 1
    
    def get_performance_summary(self):
        if not self.metrics['request_times']:
            return {
                'uptime_seconds': time.time() - self.start_time,
                'total_requests': 0, 'avg_response_time': 0,
                'max_response_time': 0, 'slow_requests_count': 0,
                'error_counts': {}, 'top_endpoints': []
            }
        
        response_times = [req['response_time'] for req in self.metrics['request_times']]
        return {
            'uptime_seconds': time.time() - self.start_time,
            'total_requests': len(self.metrics['request_times']),
            'avg_response_time': sum(response_times) / len(response_times),
            'max_response_time': max(response_times),
            'slow_requests_count': len(self.metrics['slow_requests']),
            'error_counts': dict(self.metrics['error_count']),
            'top_endpoints': self._get_top_endpoints()
        }
    
    def _get_top_endpoints(self):
        sorted_endpoints = sorted(
            self.metrics['endpoint_stats'].items(),
            key=lambda x: x[1]['count'], reverse=True
        )
        return [
            {
                'endpoint': endpoint, 'count': stats['count'],
                'avg_time': round(stats['avg_time'], 2),
                'max_time': round(stats['max_time'], 2)
            }
            for endpoint, stats in sorted_endpoints[:5]
        ]
    
    def get_slow_requests(self):
        return [
            {
                'time': req['time'].isoformat(),
                'endpoint': req['endpoint'],
                'method': req['method'],
                'response_time': req['response_time'],
                'status_code': req['status_code']
            }
            for req in list(self.metrics['slow_requests'])[-10:]
        ]

# 創建性能監控實例
performance_monitor = SimplePerformanceMonitor()
PERFORMANCE_MONITORING_AVAILABLE = True

# 導入所有 Blueprint


def setup_logging(app):
    """設置日誌系統"""
    # 確保日誌目錄存在
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 設置日誌格式
    formatter = logging.Formatter(
        '%(asctime)s %(levelname)s [%(filename)s:%(lineno)d] - %(message)s'
    )
    
    # 應用主日誌
    app_handler = RotatingFileHandler(
        'logs/accounting.log',
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=10
    )
    app_handler.setFormatter(formatter)
    app_handler.setLevel(logging.INFO)
    
    # 錯誤日誌
    error_handler = RotatingFileHandler(
        'logs/error.log',
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=5
    )
    error_handler.setFormatter(formatter)
    error_handler.setLevel(logging.ERROR)
    
    # 訪問日誌
    access_handler = RotatingFileHandler(
        'logs/access.log',
        maxBytes=5 * 1024 * 1024,
        backupCount=10
    )
    access_formatter = logging.Formatter(
        '%(asctime)s - %(message)s'
    )
    access_handler.setFormatter(access_formatter)
    access_handler.setLevel(logging.INFO)
    
    # 配置應用日誌
    app.logger.addHandler(app_handler)
    app.logger.addHandler(error_handler)
    app.logger.setLevel(logging.INFO)
    
    # 創建訪問日誌記錄器
    access_logger = logging.getLogger('access')
    access_logger.addHandler(access_handler)
    access_logger.setLevel(logging.INFO)
    access_logger.propagate = False
    
    # 記錄啟動信息
    app.logger.info('會計系統啟動完成')
    app.logger.info(f'日誌系統初始化完成 - 日誌目錄: {os.path.abspath("logs")}')

def create_app():
    """建立 Flask 應用程式"""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # 設置日誌系統
    setup_logging(app)
    
    # 設置性能監控
    def setup_simple_performance_monitoring(app):
        @app.before_request
        def before_request():
            g.start_time = time.time()
        
        @app.after_request
        def after_request(response):
            if hasattr(g, 'start_time'):
                response_time = (time.time() - g.start_time) * 1000
                
                # 記錄到簡單版監控
                performance_monitor.record_request(
                    endpoint=request.endpoint or request.path,
                    method=request.method,
                    response_time=response_time,
                    status_code=response.status_code
                )
                
                # 同時記錄到完整版監控
                from utils.performance_monitor import performance_monitor as full_monitor
                full_monitor.record_request(
                    endpoint=request.endpoint or 'unknown',
                    method=request.method,
                    response_time=response_time / 1000,  # 轉換為秒
                    status_code=response.status_code
                )
            return response
        
        # @app.route('/admin/performance')
        # def performance_dashboard():
        #     return render_template('simple_performance.html')
        
        # @app.route('/admin/performance/api')
        # def performance_api():
        #     summary = performance_monitor.get_performance_summary()
        #     slow_requests = performance_monitor.get_slow_requests()
        #     return jsonify({
        #         'performance_summary': summary,
        #         'slow_requests': slow_requests,
        #         'timestamp': datetime.now(timezone(timedelta(hours=8))).replace(microsecond=0, tzinfo=None).isoformat()
        #     })
    
    setup_simple_performance_monitoring(app)
    
    # 添加請求日誌中間件
    @app.before_request
    def before_request():
        from flask import g, request
        g.start_time = time.time()
        app.logger.info(f'請求開始: {request.method} {request.url}')
    
    @app.after_request
    def after_request(response):
        from flask import g, request
        if hasattr(g, 'start_time'):
            response_time = (time.time() - g.start_time) * 1000
            
            # 記錄訪問日誌
            access_logger = logging.getLogger('access')
            access_logger.info(f'{request.remote_addr or "unknown"} - {request.method} {request.url} - {response.status_code} - {response_time:.2f}ms')
            
            # 記錄慢請求
            if response_time > 2000:
                app.logger.warning(f'慢請求: {request.method} {request.url} - {response_time:.2f}ms')
            
            app.logger.info(f'請求完成: {response.status_code} - {response_time:.2f}ms')
        
        return response
    
    # 註冊所有 Blueprint
    app.register_blueprint(main_bp)
    app.register_blueprint(income_expense_bp)
    app.register_blueprint(assets_bp)
    app.register_blueprint(payroll_bp)
    app.register_blueprint(service_reward_bp)
    app.register_blueprint(settings_bp)
    app.register_blueprint(accounting_other_bp)
    app.register_blueprint(accounting_bp, url_prefix='/accounting')
    app.register_blueprint(account_bp)
    app.register_blueprint(monitoring_bp)
    app.register_blueprint(share_account_bp)
    
    # 初始化資料庫
    init_db()
    
    # 註冊錯誤處理
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return render_template('errors/500.html'), 500
    
    # 取得所有總行
    @app.route('/api/bank_heads')
    def api_bank_heads():
        from bank_data import get_head_offices
        return jsonify(get_head_offices())

    # 取得某總行下所有分行
    @app.route('/api/bank_branches/<head_code>')
    def api_bank_branches(head_code):
        from bank_data import get_branches
        return jsonify(get_branches(head_code))

    # 新增：檢查今年發票號碼是否存在
    @app.route('/api/check_invoice_number')
    def api_check_invoice_number():
        from model import Money
        from sqlalchemy import extract
        from database import db_session
        import datetime
        number = request.args.get('number', '').strip()
        if not number:
            return jsonify({'exists': False, 'msg': '未提供號碼'})
        this_year = datetime.datetime.now().year
        exists = db_session.query(Money).filter(
            Money.number == number,
            Money.date != None,
            extract('year', Money.date) == this_year
        ).first() is not None
        return jsonify({'exists': exists})
    
    @app.route('/debug_form')
    def debug_form():
        """調試表單頁面"""
        return render_template('debug_form.html')
    
    @app.route('/transfer/add')
    def transfer_add():
        """資金移轉紀錄新增表單"""
        return render_template('transfer_form.html')
    
    @app.route('/transfer/list')
    def transfer_list():
        """資金移轉紀錄列表頁"""
        from data.menu_data import menu
        main_menu = list(menu.keys())
        selected = '資金管理'
        # 這裡 transfer_records 可從資料庫查詢，暫時給空 list
        transfer_records = []
        return render_template('transfer_list.html',
                            transfer_records=transfer_records,
                            sidebar_items=main_menu,
                            selected=selected)
    
    @app.route('/transfer', methods=['GET', 'POST'])
    def transfer():
        from data.menu_data import menu
        from model import Account, Transfer
        from database import db_session
        import datetime

        sidebar_items = list(menu.keys())
        selected = '資金管理'
        submenus = menu[selected]
        accounts = db_session.query(Account).all()

        if request.method == 'POST':
            out_account_id = request.form.get('out_account')
            in_account_id = request.form.get('in_account')
            subject_out = request.form.get('subject_out')
            subject_in = request.form.get('subject_in')
            amount = request.form.get('amount')
            fee = request.form.get('fee')
            note = request.form.get('note')
            transfer_date_str = request.form.get('transfer_date')
            transfer_date = None
            if transfer_date_str:
                transfer_date = datetime.datetime.strptime(transfer_date_str, '%Y-%m-%d').date()
            # 檔案處理略（可加上傳邏輯）
            voucher = None

            transfer = Transfer(
                out_account_id=out_account_id,
                in_account_id=in_account_id,
                subject_code=subject_out,  # 這裡存轉出帳號的科目
                amount=amount,
                fee=fee,
                note=note,
                transfer_date=transfer_date,
                voucher=voucher
            )
            db_session.add(transfer)
            db_session.commit()
            return redirect('/transfer/list')

        return render_template(
            'index.html',
            sidebar_items=sidebar_items,
            selected=selected,
            submenus=submenus,
            transfer_form=True,
            accounts=accounts
        )
    
    @app.route('/bankloan/create')
    def bankloan_create():
        """新增銀行借款表單頁"""
        from data.menu_data import menu
        main_menu = list(menu.keys())
        selected = '資金管理'
        return render_template('bankloan_create.html',
                            sidebar_items=main_menu,
                            selected=selected)

    @app.route('/bankloan/list')
    def bankloan_list():
        """銀行借款列表頁"""
        from data.menu_data import menu
        from model import Transfer
        from database import db_session
        main_menu = list(menu.keys())
        selected = '資金管理'
        # 從 BankLoan 資料表查詢所有銀行借款紀錄
        bankloan_records = db_session.query(Transfer).all()
        return render_template('bankloan_list.html',
                            bankloan_records=bankloan_records,
                            sidebar_items=main_menu,
                            selected=selected)

    @app.route('/bankloan/edit/<int:loan_id>')
    def bankloan_edit(loan_id):
        """編輯銀行借款頁面（實際上是 Transfer 資料表）"""
        from data.menu_data import menu
        from model import Transfer
        from database import db_session
        main_menu = list(menu.keys())
        selected = '資金管理'
        # 查詢該筆 Transfer 資料
        loan_record = db_session.query(Transfer).filter_by(id=loan_id).first()
        return render_template('bankloan_create.html',
                             sidebar_items=main_menu,
                             selected=selected,
                             edit_mode=True,
                             loan_record=loan_record)

    @app.route('/bankloan/delete/<int:loan_id>', methods=['DELETE'])
    def bankloan_delete(loan_id):
        """刪除銀行借款記錄"""
        try:
            # 這裡可以實作刪除邏輯
            # delete_bankloan_by_id(loan_id)
            return jsonify({'success': True, 'message': '刪除成功'})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/fund_record/create', methods=['GET', 'POST'])
    def fund_record_create():
        """新增資金紀錄頁面"""
        from data.menu_data import menu
        main_menu = list(menu.keys())
        selected = '資金管理'

        if request.method == 'POST':
            # 這裡可以處理表單提交的資料
            # 暫時只是重定向回表單頁面
            return redirect('/fund_record/create')

        return render_template('fund_record_create.html',
                             sidebar_items=main_menu,
                             selected=selected)

    @app.route('/fund_record/list')
    def fund_record_list():
        """資金紀錄列表頁面"""
        from data.menu_data import menu
        from model import Transfer
        from database import db_session
        main_menu = list(menu.keys())
        selected = '資金管理'
        # 從 Transfer 資料表查詢所有資金移轉紀錄
        fund_records = db_session.query(Transfer).all()
        return render_template('fund_record_list.html',
                             fund_records=fund_records,
                             sidebar_items=main_menu,
                             selected=selected)

    @app.route('/fund_record/edit/<int:record_id>', methods=['GET', 'POST'])
    def fund_record_edit(record_id):
        """編輯資金紀錄頁面"""
        from data.menu_data import menu
        main_menu = list(menu.keys())
        selected = '資金管理'

        if request.method == 'POST':
            # 這裡可以處理表單提交的資料
            return redirect('/fund_record/list')

        # 這裡可以從資料庫查詢特定的資金紀錄
        # record = get_fund_record_by_id(record_id)
        return render_template('fund_record_create.html',
                             sidebar_items=main_menu,
                             selected=selected,
                             edit_mode=True,
                             record_id=record_id)

    @app.route('/fund_record/delete/<int:record_id>', methods=['DELETE'])
    def fund_record_delete(record_id):
        """刪除資金紀錄"""
        try:
            # 這裡可以實作刪除邏輯
            # delete_fund_record_by_id(record_id)
            return jsonify({'success': True, 'message': '刪除成功'})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/investment_manage')
    def investment_manage():
        """投資管理頁面"""
        from data.menu_data import menu
        main_menu = list(menu.keys())
        selected = '資金管理'
        # 這裡可以從資料庫查詢投資記錄，暫時給空 list
        investments = []
        return render_template('investment_manage.html',
                             investments=investments,
                             sidebar_items=main_menu,
                             selected=selected)

    @app.route('/investment/create', methods=['GET', 'POST'])
    def investment_create():
        """新增投資記錄頁面"""
        from data.menu_data import menu
        main_menu = list(menu.keys())
        selected = '資金管理'

        if request.method == 'POST':
            # 這裡可以處理表單提交的資料
            return redirect('/investment_manage')

        return render_template('investment_create.html',
                             sidebar_items=main_menu,
                             selected=selected)

    @app.route('/investment/edit/<int:investment_id>', methods=['GET', 'POST'])
    def investment_edit(investment_id):
        """編輯投資記錄頁面"""
        from data.menu_data import menu
        main_menu = list(menu.keys())
        selected = '資金管理'

        if request.method == 'POST':
            # 這裡可以處理表單提交的資料
            return redirect('/investment_manage')

        # 這裡可以從資料庫查詢特定的投資記錄
        # investment = get_investment_by_id(investment_id)
        return render_template('investment_create.html',
                             sidebar_items=main_menu,
                             selected=selected,
                             edit_mode=True,
                             investment_id=investment_id)

    @app.route('/investment/delete/<int:investment_id>', methods=['DELETE'])
    def investment_delete(investment_id):
        """刪除投資記錄"""
        try:
            # 這裡可以實作刪除邏輯
            # delete_investment_by_id(investment_id)
            return jsonify({'success': True, 'message': '刪除成功'})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/account_detail')
    def account_detail():
        """帳戶明細頁面"""
        from data.menu_data import menu
        main_menu = list(menu.keys())
        selected = '資金管理'

        # 取得查詢參數
        account_id = request.args.get('account_id', type=int)

        # 這裡可以從資料庫查詢帳戶資訊和交易記錄
        # account_info = get_account_by_id(account_id) if account_id else None
        # transactions = get_account_transactions(account_id) if account_id else []

        # 模擬帳戶資料
        account_info = {
            'id': 1,
            'name': '永豐銀行存款戶',
            'account_number': '***********',
            'balance': 52100,
            'bank_name': '永豐銀行',
            'branch': '某分行'
        } if account_id else None

        # 模擬交易記錄
        transactions = [
            {
                'date': '2025-07-26',
                'amount_in': 52100,
                'amount_out': 0,
                'balance': 52100,
                'description': '期初餘額',
                'note': '開帳',
                'voucher': '收入-1-某某公司'
            },
            {
                'date': '2025-06-30',
                'amount_in': 0,
                'amount_out': 50000,
                'balance': 50000,
                'description': '轉帳支出',
                'note': '工程款',
                'voucher': '支出-2-某某公司'
            }
        ] if account_id else []

        # 取得所有帳戶列表供選擇
        # all_accounts = get_all_accounts()
        all_accounts = [
            {'id': 1, 'name': '永豐銀行存款戶', 'account_number': '***********'},
            {'id': 2, 'name': '台新銀行存款戶', 'account_number': '***********'},
            {'id': 3, 'name': '現金', 'account_number': ''}
        ]

        return render_template('account_detail.html',
                             sidebar_items=main_menu,
                             selected=selected,
                             account_info=account_info,
                             transactions=transactions,
                             all_accounts=all_accounts,
                             selected_account_id=account_id)
    
    return app


if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, port=5001)