# 資料庫優化完成報告

## 優化內容總結

### 1. 添加了缺失的 backref 關係

#### Money 表的關聯優化
```python
# 之前：只有單向關係
subject = relationship('AccountSubject')
account = relationship('Account')
payment_identity = relationship('PaymentIdentity')

# 現在：雙向關係，支援反向查詢
subject = relationship('AccountSubject', backref='money_records')
account = relationship('Account', backref='money_records')
payment_identity = relationship('PaymentIdentity', backref='money_records')
```

**好處**：
- 可以從 AccountSubject 查詢所有相關的 Money 記錄：`subject.money_records`
- 可以從 Account 查詢所有相關的 Money 記錄：`account.money_records`
- 可以從 PaymentIdentity 查詢所有相關的 Money 記錄：`payment_identity.money_records`

#### Account 表添加科目關聯
```python
# 新增
subject_code = Column(String(20), ForeignKey('account_subject.code'), index=True)
subject = relationship('AccountSubject', backref='accounts')
```

**好處**：
- Account 現在正確關聯到 AccountSubject
- 可以從 AccountSubject 查詢所有相關帳戶：`subject.accounts`

#### BankService 表添加關聯
```python
# 新增
bank = relationship("Bank", backref="services")
```

**好處**：
- 可以從 Bank 查詢所有服務：`bank.services`

### 2. 添加了重要的資料庫索引

#### 主要索引優化：
- **AccountSubject**: `code` (唯一索引), `style`, `parent_id`, `is_expandable`, `top_category`
- **Account**: `name`, `category`, `account_number`, `subject_code`, `is_default`
- **PaymentIdentity**: `type`, `name`, `tax_id`
- **Department**: `name`, `parent_id`
- **Money**: `money_type`, `a_time`, `subject_code`, `account_id`, `payment_identity_id`, `is_paid`, `should_paid_date`, `paid_date`, `created_at`

**好處**：
- 大幅提升查詢效能
- 加速常用的篩選和排序操作
- 改善外鍵關聯查詢速度

### 3. 資料完整性改進

#### 唯一約束
- `AccountSubject.code` 現在有唯一約束，防止重複的科目代碼

#### 外鍵關係完善
- `Account.subject_code` 現在正確關聯到 `AccountSubject.code`

## 查詢效能提升預期

### 常用查詢場景優化：
1. **按日期範圍查詢收支記錄** - `Money.a_time` 索引
2. **按收支類型篩選** - `Money.money_type` 索引
3. **查詢特定帳戶的交易** - `Money.account_id` 索引
4. **查詢特定科目的記錄** - `Money.subject_code` 索引
5. **查詢未收款項目** - `Money.is_paid` 索引
6. **按客戶查詢交易** - `PaymentIdentity.name`, `PaymentIdentity.type` 索引

## 新增的查詢能力

### 反向關聯查詢：
```python
# 查詢某個科目的所有交易記錄
subject = session.query(AccountSubject).filter_by(code='4100').first()
transactions = subject.money_records

# 查詢某個帳戶的所有交易記錄
account = session.query(Account).filter_by(name='現金').first()
transactions = account.money_records

# 查詢某個客戶的所有交易記錄
customer = session.query(PaymentIdentity).filter_by(name='ABC公司').first()
transactions = customer.money_records

# 查詢某個科目下的所有帳戶
subject = session.query(AccountSubject).filter_by(code='1100').first()
accounts = subject.accounts
```

## 建議的後續優化

### 1. 資料庫遷移
由於添加了新的索引和約束，建議執行資料庫遷移：
```bash
alembic revision --autogenerate -m "Add indexes and backref relationships"
alembic upgrade head
```

### 2. 查詢優化
利用新的索引和關聯關係重構現有查詢，提升效能。

### 3. 資料驗證
確保現有資料符合新的唯一約束要求。

## 注意事項

1. **AccountSubject.code 唯一約束**：確保現有資料中沒有重複的科目代碼
2. **外鍵關聯**：確保 Account.subject_code 的值都存在於 AccountSubject.code 中
3. **索引維護**：新增索引會稍微增加寫入操作的成本，但大幅提升查詢效能

## 結論

這次優化大幅改善了資料庫的：
- **查詢效能** - 通過添加關鍵索引
- **資料完整性** - 通過完善外鍵關係和約束
- **開發便利性** - 通過添加 backref 支援雙向關聯查詢
- **可維護性** - 通過統一和完善模型定義

建議在正式環境部署前，先在測試環境驗證所有功能正常運作。