# 📊 資料庫索引分析與建議

## 🤔 小規模資料是否需要索引？

### 簡短答案：**視情況而定**

對於小規模資料（< 10,000 筆記錄），索引的必要性取決於：

## 📋 索引決策矩陣

### ✅ **建議添加索引的情況**

#### 1. 頻繁查詢的欄位
```sql
-- 即使只有 1000 筆記錄，如果經常這樣查詢：
SELECT * FROM account_subject WHERE code = '1111';
-- 建議在 code 欄位加索引
```

#### 2. 外鍵關聯
```sql
-- 表關聯查詢，即使小資料量也建議索引：
SELECT * FROM account WHERE subject_code = '1111';
-- 建議在 subject_code 加索引
```

#### 3. 排序和分組
```sql
-- 經常需要排序的欄位：
SELECT * FROM transaction ORDER BY created_date DESC;
-- 建議在 created_date 加索引
```

#### 4. 唯一性約束
```sql
-- 需要確保唯一性的欄位：
SELECT * FROM users WHERE username = 'admin';
-- 建議在 username 加唯一索引
```

### ❌ **不建議添加索引的情況**

#### 1. 很少查詢的欄位
```sql
-- 如果很少這樣查詢：
SELECT * FROM account WHERE note LIKE '%備註%';
-- 不建議在 note 加索引
```

#### 2. 頻繁更新的欄位
```sql
-- 如果經常更新：
UPDATE account SET balance = balance + 1000;
-- balance 欄位索引會影響更新性能
```

#### 3. 小表（< 100 筆記錄）
- 全表掃描比索引查詢更快
- 索引維護成本大於收益

## 🔍 您的會計系統分析

### 當前資料庫結構
基於您的 `model.py`，主要表格包括：

#### 1. AccountSubject（會計科目）
```python
# 建議的索引
class AccountSubject(Base):
    code = Column(String(20), nullable=False, index=True)  # ✅ 建議加索引
    name = Column(String(100), nullable=False, index=True) # ✅ 建議加索引
    parent_id = Column(Integer, ForeignKey('account_subject.id'), index=True) # ✅ 建議加索引
```

**理由**：
- `code` - 經常用於查詢特定科目
- `name` - 可能用於搜索功能
- `parent_id` - 用於樹狀結構查詢

#### 2. Account（帳戶）
```python
# 建議的索引
class Account(Base):
    subject_code = Column(String(20), index=True)  # ✅ 建議加索引
    category = Column(String(50), index=True)      # ✅ 建議加索引
    is_default = Column(Boolean, index=True)       # ✅ 建議加索引
```

**理由**：
- `subject_code` - 關聯查詢
- `category` - 按類別篩選
- `is_default` - 查找預設帳戶

#### 3. User（用戶）
```python
# 建議的索引
class User(Base):
    username = Column(String(80), unique=True, nullable=False, index=True)  # ✅ 建議加索引
    email = Column(String(120), unique=True, nullable=False, index=True)    # ✅ 建議加索引
```

**理由**：
- `username` - 登入驗證
- `email` - 用戶查找

## 📈 性能影響分析

### 小規模資料（< 1,000 筆）
- **查詢提升**：10-50% 性能提升
- **插入影響**：5-15% 性能下降
- **儲存成本**：10-20% 額外空間

### 中規模資料（1,000-10,000 筆）
- **查詢提升**：50-300% 性能提升
- **插入影響**：10-25% 性能下降
- **儲存成本**：15-30% 額外空間

### 大規模資料（> 10,000 筆）
- **查詢提升**：300-1000% 性能提升
- **插入影響**：15-30% 性能下降
- **儲存成本**：20-40% 額外空間

## 🎯 針對您系統的建議

### 立即建議添加的索引

#### 1. 核心業務索引
```sql
-- 會計科目代碼（最重要）
CREATE INDEX idx_account_subject_code ON account_subject(code);

-- 會計科目名稱（搜索用）
CREATE INDEX idx_account_subject_name ON account_subject(name);

-- 父子關係（樹狀查詢）
CREATE INDEX idx_account_subject_parent ON account_subject(parent_id);
```

#### 2. 帳戶相關索引
```sql
-- 帳戶科目關聯
CREATE INDEX idx_account_subject_code ON account(subject_code);

-- 帳戶類別
CREATE INDEX idx_account_category ON account(category);

-- 預設帳戶查詢
CREATE INDEX idx_account_default ON account(is_default);
```

#### 3. 用戶認證索引
```sql
-- 用戶名（登入）
CREATE UNIQUE INDEX idx_user_username ON user(username);

-- 電子郵件
CREATE UNIQUE INDEX idx_user_email ON user(email);
```

### 暫時不建議的索引

#### 1. 文字欄位
```sql
-- 不建議對這些欄位加索引
-- note, description, address 等長文字欄位
```

#### 2. 很少查詢的欄位
```sql
-- 創建時間（除非需要按時間排序）
-- 更新時間（除非需要按時間篩選）
```

## 🔧 實施建議

### 階段性實施

#### 第一階段：核心索引（立即實施）
1. `account_subject.code` - 最高優先級
2. `account.subject_code` - 關聯查詢
3. `user.username` - 登入性能

#### 第二階段：業務索引（資料量增長後）
1. `account_subject.name` - 搜索功能
2. `account.category` - 分類查詢
3. `account_subject.parent_id` - 樹狀查詢

#### 第三階段：優化索引（大量資料後）
1. 複合索引
2. 部分索引
3. 函數索引

### 監控指標

#### 何時添加索引
- 查詢時間 > 100ms
- 資料量 > 1,000 筆
- 查詢頻率 > 每分鐘10次

#### 何時移除索引
- 查詢頻率 < 每天1次
- 更新頻率 > 查詢頻率
- 儲存空間不足

## 📊 性能測試建議

### 測試方法
```python
import time

def test_query_performance():
    # 無索引查詢
    start = time.time()
    result = session.query(AccountSubject).filter_by(code='1111').first()
    no_index_time = time.time() - start
    
    # 添加索引後再測試
    # CREATE INDEX idx_code ON account_subject(code);
    
    start = time.time()
    result = session.query(AccountSubject).filter_by(code='1111').first()
    with_index_time = time.time() - start
    
    improvement = (no_index_time - with_index_time) / no_index_time * 100
    print(f"性能提升: {improvement:.1f}%")
```

## 🎯 結論與建議

### 對於您的會計系統

#### ✅ **建議立即添加索引**：
1. **會計科目代碼** - 核心業務查詢
2. **用戶名** - 登入性能
3. **帳戶科目關聯** - 關聯查詢

#### 🟡 **觀察後決定**：
1. **會計科目名稱** - 如果有搜索功能
2. **帳戶類別** - 如果經常按類別篩選
3. **時間欄位** - 如果需要按時間排序

#### ❌ **暫時不需要**：
1. **備註欄位** - 很少查詢
2. **地址欄位** - 不用於查詢條件
3. **長文字欄位** - 全文搜索另外處理

### 最佳實踐

1. **先測量，再優化** - 使用性能監控確定瓶頸
2. **漸進式添加** - 從最重要的索引開始
3. **定期檢查** - 監控索引使用情況
4. **平衡考慮** - 查詢性能 vs 更新性能

您想要我幫您實際添加這些建議的索引嗎？