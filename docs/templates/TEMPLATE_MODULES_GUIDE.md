# 📋 模組化模板使用指南

## 🎯 概述

為了提高代碼的可維護性和重用性，我們將常用的 HTML 組件模組化，類似於 `sidebar.html` 的使用方式。

## 📦 可用的模組

### 1. 憑證功能模組 (`voucher_section.html`)
**用途**：處理發票、收據等憑證相關功能

**包含功能**：
- ✅ 憑證狀態選擇（憑證尚未取得/沒有憑證/有憑證）
- ✅ 發票號碼輸入與即時檢查
- ✅ 稅別選擇（應稅/免稅/零稅率）
- ✅ 發票日期
- ✅ 買方/賣方統編
- ✅ 收據類憑證選項

**使用方式**：
```html
<!-- 在表單中引入憑證模組 -->
{% include 'voucher_section.html' %}
```

### 2. 更多功能模組 (`more_section.html`)
**用途**：提供進階功能的展開/收合區域

**包含功能**：
- ✅ 部門別選擇
- ✅ 專案別選擇
- ✅ 標籤輸入
- ✅ 備註文字區域
- ✅ 檔案上傳功能
- ✅ 展開/收合切換

**使用方式**：
```html
<!-- 在表單中引入更多功能模組 -->
{% include 'more_section.html' %}
```

### 3. 收付款狀態模組 (`payment_status_section.html`)
**用途**：處理收付款狀態和日期

**包含功能**：
- ✅ 收付款狀態切換（未收付款/已收付款）
- ✅ 應收付日期
- ✅ 實收付日期（條件顯示）
- ✅ 自動狀態管理

**使用方式**：
```html
<!-- 在表單中引入收付款狀態模組 -->
{% include 'payment_status_section.html' %}
```

## 🚀 使用範例

### 完整的收入/支出表單
```html
<form method="post" enctype="multipart/form-data">
    <div class="columns">
        <div class="column is-6">
            <!-- 基本資料欄位 -->
            <div class="field">
                <label class="label">記帳時間</label>
                <input class="input" type="date" name="a_time">
            </div>
            
            <!-- 使用更多功能模組 -->
            {% include 'more_section.html' %}
        </div>
        
        <div class="column is-6">
            <!-- 使用憑證模組 -->
            {% include 'voucher_section.html' %}
            
            <!-- 使用收付款狀態模組 -->
            {% include 'payment_status_section.html' %}
        </div>
    </div>
    
    <button type="submit" class="button is-primary">儲存</button>
</form>
```

## 🔧 自訂配置

### 需要的後端資料
確保在路由中提供以下資料：

```python
@app.route('/your_form')
def your_form():
    return render_template('your_template.html',
        departments=departments,  # 部門列表
        projects=projects,        # 專案列表
        company_id=company_id     # 公司統編
    )
```

### 表單欄位名稱
模組使用的標準欄位名稱：

**憑證模組**：
- `is_paper` - 是否為收據類憑證
- `invoice_number` - 發票號碼
- `tax_type` - 稅別
- `invoice_date` - 發票日期
- `buyer_tax_id` - 買方統編
- `seller_tax_id` - 賣方統編

**更多功能模組**：
- `department_id` - 部門ID
- `project_id` - 專案ID
- `tag` - 標籤
- `note` - 備註
- `other_file` - 上傳檔案

**收付款狀態模組**：
- `is_paid` - 是否已收付款
- `should_paid_date` - 應收付日期
- `paid_date` - 實收付日期

## ✨ 優勢

### 1. 代碼重用性
- 🔄 一次開發，多處使用
- 🎯 統一的使用者體驗
- 🛠️ 集中維護和更新

### 2. 維護性
- 📝 修改一個模組影響所有使用的頁面
- 🐛 Bug 修復更加高效
- 🔧 功能增強更容易實施

### 3. 一致性
- 🎨 統一的樣式和行為
- 📋 標準化的表單欄位
- ⚡ 一致的 JavaScript 功能

## 📋 最佳實踐

### 1. 模組命名
- 使用描述性的檔名
- 以 `_section.html` 結尾
- 放在 `templates/` 目錄下

### 2. 依賴管理
- 確保 Bulma CSS 和 Font Awesome 已載入
- 模組內的 JavaScript 使用 `DOMContentLoaded` 事件
- 避免 ID 衝突

### 3. 擴展性
- 可以通過參數傳遞自訂配置
- 支援條件顯示/隱藏
- 易於添加新功能

## 🎉 已更新的頁面

- ✅ `income_record.html` - 已使用模組化組件
- ✅ `expense_record_example.html` - 範例頁面

## 🔮 未來計劃

1. **更多模組**：
   - 科目選擇模組
   - 帳戶選擇模組
   - 收支對象模組

2. **進階功能**：
   - 模組參數化配置
   - 條件渲染支援
   - 主題自訂

3. **文檔完善**：
   - 更多使用範例
   - 自訂指南
   - 最佳實踐更新

---

這種模組化方式讓您的會計系統更加靈活和易於維護！🚀