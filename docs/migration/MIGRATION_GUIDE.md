# 會計系統重構遷移指南

## 🎯 遷移目標

將原本單一檔案 `main.py` (448行) 重構為模組化架構，提升程式碼的可維護性、可測試性和可擴展性。

## 📊 遷移前後對比

### 遷移前
```
accounting/
├── main.py (448行，所有路由和邏輯)
├── model.py
├── menu_data.py
└── templates/
```

### 遷移後
```
accounting/
├── app.py (主應用程式)
├── config.py (設定檔)
├── database.py (資料庫管理)
├── routes/ (路由模組)
│   ├── __init__.py
│   ├── main.py (主頁面)
│   ├── accounting.py (會計相關)
│   ├── assets.py (資產管理)
│   ├── payroll.py (薪資管理)
│   └── settings.py (設定相關)
├── services/ (業務邏輯)
│   ├── __init__.py
│   ├── subject_service.py
│   ├── company_service.py
│   └── asset_service.py
├── utils/ (工具函數)
│   ├── __init__.py
│   └── helpers.py
└── templates/
```

## 🚀 遷移步驟

### 階段一：基礎架構 (已完成 ✅)
- [x] 建立 config.py - 設定管理
- [x] 建立 database.py - 資料庫連線管理
- [x] 建立 services/subject_service.py - 科目服務層
- [x] 建立 routes/accounting_optimized.py - 優化版會計路由
- [x] 建立 app.py - 主應用程式

### 階段二：路由遷移 (已完成 ✅)
- [x] 遷移主頁面路由 (index, sidebar)
- [x] 遷移收支帳簿路由
- [x] 遷移資產管理路由
- [x] 遷移薪資報酬路由
- [x] 遷移勞務報酬路由
- [x] 遷移設定相關路由

### 階段三：服務層建立 (待進行 ⏳)
- [ ] 建立 company_service.py
- [ ] 建立 asset_service.py
- [ ] 建立 payroll_service.py
- [ ] 建立 employee_service.py

### 階段四：工具函數 (已完成 ✅)
- [x] 建立 utils/helpers.py
- [x] 建立 utils/validators.py (整合在 helpers.py 中)
- [x] 建立 utils/formatters.py (整合在 helpers.py 中)

### 階段五：測試與優化 (待進行 ⏳)
- [ ] 加入單元測試
- [ ] 加入整合測試
- [ ] 效能優化
- [ ] 錯誤處理完善

## 📝 遷移檢查清單

### 每個路由遷移時需要檢查：
- [ ] 路由函數是否正確遷移
- [ ] 資料庫連線是否使用新的 context manager
- [ ] 錯誤處理是否加入
- [ ] 模板變數是否正確傳遞
- [ ] URL 是否正確更新
- [ ] 功能是否正常運作

### 每個服務層建立時需要檢查：
- [ ] 業務邏輯是否正確封裝
- [ ] 資料庫操作是否使用 context manager
- [ ] 錯誤處理是否完善
- [ ] 型別提示是否加入
- [ ] 文件字串是否完整

## 🔧 遷移工具和命令

### 建立新模組
```bash
# 建立目錄結構
mkdir -p routes services utils

# 建立 __init__.py 檔案
touch routes/__init__.py services/__init__.py utils/__init__.py
```

### 測試遷移
```bash
# 執行新應用程式
python app.py

# 檢查是否有語法錯誤
python -m py_compile app.py
python -m py_compile routes/*.py
python -m py_compile services/*.py
```

### 備份原始檔案
```bash
# 備份原始 main.py
cp main.py main_backup.py
```

## ⚠️ 注意事項

1. **逐步遷移**：一次只遷移一個模組，確保功能正常後再繼續
2. **保持相容性**：遷移期間保持原有功能正常運作
3. **測試優先**：每個步驟都要測試功能是否正常
4. **版本控制**：使用 git 記錄每個遷移步驟
5. **文件更新**：及時更新相關文件

## 📞 問題處理

如果在遷移過程中遇到問題：
1. 檢查錯誤訊息
2. 確認依賴套件是否正確安裝
3. 檢查檔案路徑是否正確
4. 確認資料庫連線是否正常
5. 查看 Flask 的 debug 訊息

## 🎉 完成標準

當所有階段完成後，應該達到：
- [ ] 程式碼模組化，每個檔案職責單一
- [ ] 資料庫操作統一使用 context manager
- [ ] 錯誤處理完善
- [ ] 程式碼可讀性提升
- [ ] 功能測試通過
- [ ] 效能不降低或有所提升 