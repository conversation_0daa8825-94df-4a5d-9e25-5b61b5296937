# 🔄 main.py 遷移計劃

## 📋 當前狀況
- ✅ 所有功能都已遷移到 Blueprint 架構
- ✅ app.py 完全可以獨立運行
- ✅ 測試顯示所有路由正常工作

## 🎯 遷移步驟

### 第一步：備份 main.py
```bash
mv main.py main.py.backup
```

### 第二步：測試 app.py 獨立運行
```bash
python app.py
```

### 第三步：更新啟動方式
如果之前使用 `python main.py` 啟動，改為：
```bash
python app.py
```

或者使用 Flask 命令：
```bash
export FLASK_APP=app.py
flask run
```

### 第四步：更新測試和文檔
- 更新 README.md 中的啟動說明
- 更新任何引用 main.py 的腳本或配置

## ⚠️ 注意事項

### URL 路徑變化
一些會計相關的路由路徑有變化：

**舊路徑 (main.py)**：
- `/subject_manage`
- `/add_subject`  
- `/edit_subject`
- `/delete_subject`

**新路徑 (app.py)**：
- `/accounting/subject_manage`
- `/accounting/add_subject`
- `/accounting/edit_subject`
- `/accounting/delete_subject`

### 需要更新的地方
1. **前端連結** - 檢查 HTML 模板中的連結
2. **JavaScript** - 檢查 AJAX 請求的 URL
3. **重定向** - 檢查 Python 代碼中的 url_for 調用

## 🔧 快速檢查腳本

```bash
# 檢查模板中的舊路徑
grep -r "subject_manage\|add_subject\|edit_subject\|delete_subject" templates/

# 檢查 JavaScript 中的舊路徑  
find . -name "*.js" -exec grep -l "subject_manage\|add_subject\|edit_subject\|delete_subject" {} \;
```

## ✅ 驗證清單

- [ ] app.py 可以獨立啟動
- [ ] 所有頁面正常載入
- [ ] 表單提交功能正常
- [ ] 資料庫操作正常
- [ ] 前端連結都指向正確路徑
- [ ] 測試套件通過

## 🎉 完成後的好處

1. **架構更清晰** - 單一入口點
2. **維護更容易** - Blueprint 模組化設計
3. **擴展性更好** - 易於添加新功能
4. **代碼更乾淨** - 移除重複代碼