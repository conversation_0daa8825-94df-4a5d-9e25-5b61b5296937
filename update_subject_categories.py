#!/usr/bin/env python3
"""
更新科目分類的腳本
根據科目代碼自動設定 top_category
"""

from model import AccountSubject, session

def update_subject_categories():
    """根據科目代碼更新分類"""
    
    # 定義科目代碼範圍對應的分類
    category_mapping = {
        '資產': ['11', '12', '13', '14', '15', '16', '17', '18', '19'],
        '負債': ['21', '22', '23', '24', '25', '26', '27', '28', '29'],
        '權益': ['31', '32', '33', '34', '35', '36', '37', '38', '39'],
        '營業收入': ['41', '42', '43', '44', '45', '46', '47', '48', '49'],
        '營業成本': ['51', '52', '53', '54', '55', '56', '57', '58', '59'],
        '營業費用': ['61', '62', '63', '64', '65', '66', '67', '68', '69'],
        '營業外收益及費損': ['71', '72', '73', '74', '75', '76', '77', '78', '79', '81', '82', '83', '84', '85', '86', '87', '88', '89']
    }
    
    try:
        # 查詢所有科目
        subjects = session.query(AccountSubject).all()
        updated_count = 0
        
        for subject in subjects:
            # 取得科目代碼的前兩位
            if len(subject.code) >= 2:
                code_prefix = subject.code[:2]
                
                # 找到對應的分類
                for category, prefixes in category_mapping.items():
                    if code_prefix in prefixes:
                        if subject.top_category != category:
                            print(f"更新科目 {subject.code} {subject.name}: {subject.top_category} -> {category}")
                            subject.top_category = category
                            updated_count += 1
                        break
                else:
                    # 如果找不到對應分類，設為其他
                    if subject.top_category != '其他':
                        print(f"設定科目 {subject.code} {subject.name} 為其他分類")
                        subject.top_category = '其他'
                        updated_count += 1
        
        # 提交變更
        session.commit()
        print(f"\n✅ 成功更新 {updated_count} 個科目的分類")
        
        # 顯示分類統計
        print("\n📊 分類統計:")
        for category in category_mapping.keys():
            count = session.query(AccountSubject).filter_by(top_category=category).count()
            print(f"  {category}: {count} 個科目")
        
        other_count = session.query(AccountSubject).filter_by(top_category='其他').count()
        print(f"  其他: {other_count} 個科目")
        
    except Exception as e:
        session.rollback()
        print(f"❌ 更新失敗: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    update_subject_categories()
