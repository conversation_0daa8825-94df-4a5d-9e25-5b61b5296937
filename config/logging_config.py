"""
日誌配置模組
"""
import logging
import os
from logging.handlers import RotatingFileHandler
from datetime import datetime, timezone, timedelta

class LoggingConfig:
    """日誌配置類"""
    
    @staticmethod
    def setup_logging(app):
        """設置應用程序日誌"""
        # 確保日誌目錄存在
        log_dir = 'logs'
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 設置日誌格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 應用日誌
        app_handler = RotatingFileHandler(
            os.path.join(log_dir, 'accounting.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        app_handler.setFormatter(formatter)
        app_handler.setLevel(logging.INFO)
        
        # 錯誤日誌
        error_handler = RotatingFileHandler(
            os.path.join(log_dir, 'error.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        error_handler.setFormatter(formatter)
        error_handler.setLevel(logging.ERROR)
        
        # 添加處理器到應用
        app.logger.addHandler(app_handler)
        app.logger.addHandler(error_handler)
        app.logger.setLevel(logging.INFO)
        
        return app

class SecurityLogger:
    """安全日誌記錄器"""
    
    def __init__(self):
        self.logger = logging.getLogger('security')
        self.logger.setLevel(logging.INFO)
        
        # 確保日誌目錄存在
        log_dir = 'logs'
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 安全日誌處理器
        handler = RotatingFileHandler(
            os.path.join(log_dir, 'security.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - SECURITY - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        
        if not self.logger.handlers:
            self.logger.addHandler(handler)
    
    def log_login_attempt(self, username, success, ip_address):
        """記錄登入嘗試"""
        status = "SUCCESS" if success else "FAILED"
        self.logger.info(f"Login attempt - User: {username}, Status: {status}, IP: {ip_address}")
    
    def log_suspicious_activity(self, activity, ip_address, details=""):
        """記錄可疑活動"""
        self.logger.warning(f"Suspicious activity - {activity}, IP: {ip_address}, Details: {details}")

class PerformanceLogger:
    """性能日誌記錄器"""
    
    def __init__(self):
        self.logger = logging.getLogger('performance')
        self.logger.setLevel(logging.INFO)
        
        # 確保日誌目錄存在
        log_dir = 'logs'
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 性能日誌處理器
        handler = RotatingFileHandler(
            os.path.join(log_dir, 'performance.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - PERFORMANCE - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        
        if not self.logger.handlers:
            self.logger.addHandler(handler)
    
    def log_slow_request(self, endpoint, method, duration, ip_address):
        """記錄慢請求"""
        self.logger.warning(f"Slow request - {method} {endpoint}, Duration: {duration:.3f}s, IP: {ip_address}")
    
    def log_request(self, endpoint, method, duration, status_code, ip_address):
        """記錄一般請求"""
        self.logger.info(f"Request - {method} {endpoint}, Duration: {duration:.3f}s, Status: {status_code}, IP: {ip_address}")

# 創建全局實例
security_logger = SecurityLogger()
performance_logger = PerformanceLogger()