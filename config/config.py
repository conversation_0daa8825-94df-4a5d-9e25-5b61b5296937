import os

class Config:
    # 資料庫設定
    @staticmethod
    def get_database_uri():
        # 統一使用專案根目錄的資料庫
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        db_path = os.path.join(project_root, 'app.db')
        return f"sqlite:///{db_path}"
    
    # Flask 設定
    SECRET_KEY = os.environ.get('SECRET_KEY') or '2d9e14a036d3e1a19e2b86f34d1a62339399963073bded7c494e6a76a2e1616a'
    DEBUG = True
    
    # 其他設定
    ITEMS_PER_PAGE = 20 
    
    # 環境配置
    @staticmethod
    def get_config():
        """根據環境變數選擇配置"""
        env = os.environ.get('FLASK_ENV', 'development')
        if env == 'production':
            return ProductionConfig
        elif env == 'testing':
            return TestingConfig
        else:
            return DevelopmentConfig

class DevelopmentConfig(Config):
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    DEBUG = False
    TESTING = False

class TestingConfig(Config):
    DEBUG = True
    TESTING = True
    WTF_CSRF_ENABLED = False
