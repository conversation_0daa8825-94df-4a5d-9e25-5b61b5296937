#!/usr/bin/env python3
"""為會計系統添加資料庫索引"""

from sqlalchemy import text
from database import engine

def add_indexes():
    """添加建議的資料庫索引"""
    
    print("🔧 開始添加資料庫索引...")
    
    # 建議的索引列表
    indexes = [
        # 1. 會計科目表 - 最重要的索引
        {
            'name': 'idx_account_subject_code',
            'table': 'account_subject',
            'column': 'code',
            'reason': '會計科目代碼查詢（最頻繁）',
            'priority': 'HIGH'
        },
        {
            'name': 'idx_account_subject_parent_id',
            'table': 'account_subject', 
            'column': 'parent_id',
            'reason': '父子關係查詢（樹狀結構）',
            'priority': 'HIGH'
        },
        {
            'name': 'idx_account_subject_name',
            'table': 'account_subject',
            'column': 'name',
            'reason': '科目名稱搜索',
            'priority': 'MEDIUM'
        },
        
        # 2. 帳戶表索引
        {
            'name': 'idx_account_subject_code',
            'table': 'account',
            'column': 'subject_code', 
            'reason': '帳戶與科目關聯查詢',
            'priority': 'HIGH'
        },
        {
            'name': 'idx_account_category',
            'table': 'account',
            'column': 'category',
            'reason': '按帳戶類別篩選',
            'priority': 'MEDIUM'
        },
        {
            'name': 'idx_account_is_default',
            'table': 'account',
            'column': 'is_default',
            'reason': '查找預設帳戶',
            'priority': 'MEDIUM'
        },
        
        # 3. 用戶表索引
        {
            'name': 'idx_users_username',
            'table': 'users',
            'column': 'username',
            'reason': '用戶登入查詢',
            'priority': 'HIGH',
            'unique': True
        },
        {
            'name': 'idx_users_email', 
            'table': 'users',
            'column': 'email',
            'reason': '用戶郵箱查詢',
            'priority': 'MEDIUM',
            'unique': True
        },
        
        # 4. 付款身份表索引
        {
            'name': 'idx_payment_identity_type',
            'table': 'payment_identity',
            'column': 'type',
            'reason': '按身份類型篩選',
            'priority': 'LOW'
        },
        {
            'name': 'idx_payment_identity_tax_id',
            'table': 'payment_identity', 
            'column': 'tax_id',
            'reason': '統編查詢',
            'priority': 'MEDIUM'
        }
    ]
    
    # 按優先級分組
    high_priority = [idx for idx in indexes if idx['priority'] == 'HIGH']
    medium_priority = [idx for idx in indexes if idx['priority'] == 'MEDIUM'] 
    low_priority = [idx for idx in indexes if idx['priority'] == 'LOW']
    
    print(f"📊 計劃添加 {len(indexes)} 個索引:")
    print(f"  🔴 高優先級: {len(high_priority)} 個")
    print(f"  🟡 中優先級: {len(medium_priority)} 個") 
    print(f"  🟢 低優先級: {len(low_priority)} 個")
    
    # 執行索引創建
    success_count = 0
    error_count = 0
    
    with engine.connect() as conn:
        # 先添加高優先級索引
        for idx_group, group_name in [(high_priority, "高優先級"), (medium_priority, "中優先級"), (low_priority, "低優先級")]:
            print(f"\n🔧 添加{group_name}索引...")
            
            for idx in idx_group:
                try:
                    # 檢查索引是否已存在
                    check_sql = f"""
                    SELECT name FROM sqlite_master 
                    WHERE type='index' AND name='{idx['name']}'
                    """
                    result = conn.execute(text(check_sql)).fetchone()
                    
                    if result:
                        print(f"  ⚠️  索引 {idx['name']} 已存在，跳過")
                        continue
                    
                    # 創建索引SQL
                    unique_keyword = "UNIQUE " if idx.get('unique', False) else ""
                    create_sql = f"""
                    CREATE {unique_keyword}INDEX {idx['name']} 
                    ON {idx['table']}({idx['column']})
                    """
                    
                    conn.execute(text(create_sql))
                    conn.commit()
                    
                    print(f"  ✅ {idx['name']} - {idx['reason']}")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ❌ {idx['name']} 失敗: {str(e)}")
                    error_count += 1
    
    print("\n📊 索引添加完成:")
    print(f"  ✅ 成功: {success_count} 個")
    print(f"  ❌ 失敗: {error_count} 個")
    
    return success_count, error_count

def analyze_index_impact():
    """分析索引對性能的影響"""
    
    print("\n📈 分析索引性能影響...")
    
    with engine.connect() as conn:
        # 檢查索引使用情況
        indexes_sql = """
        SELECT name, tbl_name, sql 
        FROM sqlite_master 
        WHERE type='index' AND name LIKE 'idx_%'
        ORDER BY tbl_name, name
        """
        
        indexes = conn.execute(text(indexes_sql)).fetchall()
        
        if indexes:
            print(f"📋 當前自定義索引 ({len(indexes)} 個):")
            for idx in indexes:
                print(f"  📌 {idx[0]} -> {idx[1]} 表")
        else:
            print("⚠️  尚未發現自定義索引")
        
        # 分析表大小
        tables_sql = """
        SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'
        """
        tables = conn.execute(text(tables_sql)).fetchall()
        
        print("\n📊 表記錄數統計:")
        for (table_name,) in tables:
            try:
                count_sql = f"SELECT COUNT(*) FROM {table_name}"
                count = conn.execute(text(count_sql)).fetchone()[0]
                
                # 根據記錄數給出索引建議
                if count > 1000:
                    status = "🔴 強烈建議索引"
                elif count > 100:
                    status = "🟡 建議考慮索引" 
                else:
                    status = "🟢 索引可選"
                    
                print(f"  {table_name}: {count:,} 筆 - {status}")
                
            except Exception:
                print(f"  {table_name}: 查詢失敗")

def test_query_performance():
    """測試查詢性能"""
    
    print("\n⚡ 測試查詢性能...")
    
    import time
    from sqlalchemy.orm import sessionmaker
    from model import AccountSubject, Account
    
    Session = sessionmaker(bind=engine)
    session = Session()
    
    # 測試常見查詢
    test_queries = [
        {
            'name': '按科目代碼查詢',
            'query': lambda: session.query(AccountSubject).filter_by(code='1111').first(),
            'expected_benefit': 'HIGH'
        },
        {
            'name': '查詢子科目',
            'query': lambda: session.query(AccountSubject).filter_by(parent_id=1).all(),
            'expected_benefit': 'HIGH'
        },
        {
            'name': '按科目名稱搜索',
            'query': lambda: session.query(AccountSubject).filter(AccountSubject.name.like('%現金%')).all(),
            'expected_benefit': 'MEDIUM'
        },
        {
            'name': '查詢帳戶',
            'query': lambda: session.query(Account).filter_by(subject_code='1111').all(),
            'expected_benefit': 'HIGH'
        }
    ]
    
    for test in test_queries:
        try:
            start_time = time.time()
            #result = test['query']()
            duration = (time.time() - start_time) * 1000
            
            print(f"  📊 {test['name']}: {duration:.2f}ms (索引收益: {test['expected_benefit']})")
            
        except Exception as e:
            print(f"  ❌ {test['name']}: 查詢失敗 - {str(e)}")
    
    session.close()

if __name__ == "__main__":
    print("🗄️  資料庫索引管理工具")
    print("=" * 50)
    
    # 分析當前狀況
    analyze_index_impact()
    
    # 測試當前性能
    test_query_performance()
    
    # 詢問是否添加索引
    print("\n" + "=" * 50)
    response = input("是否要添加建議的索引？(y/N): ").lower().strip()
    
    if response in ['y', 'yes']:
        success, error = add_indexes()
        
        if success > 0:
            print(f"\n🎉 成功添加 {success} 個索引！")
            print("\n📈 建議重新測試查詢性能:")
            print("python scripts/add_database_indexes.py")
        
        if error > 0:
            print(f"\n⚠️  有 {error} 個索引添加失敗，請檢查日誌")
    else:
        print("\n💡 您可以稍後運行此腳本添加索引")
    
    print("\n📋 索引管理建議:")
    print("1. 監控查詢性能，當響應時間 > 100ms 時考慮索引")
    print("2. 當資料量增長到 > 1000 筆時，重新評估索引需求")
    print("3. 定期檢查索引使用情況，移除不必要的索引")