import re

INPUT_FILE = '/Users/<USER>/Library/CloudStorage/Dropbox/Code/python/accounting/templates/1'
OUTPUT_FILE = 'bank_data.py'

head_offices = {}
branches = {}

def normalize_line(line):
    # 把所有全形空白換成半形空白，再去除多餘空白
    line = line.replace('\u3000', ' ')
    line = re.sub(r' +', ' ', line)
    return line.strip()

with open(INPUT_FILE, encoding='utf-8') as f:
    for line in f:
        line = normalize_line(line)
        if not line:
            continue
        # 只抓開頭3~7碼+一個半形空白+剩下的名稱
        m = re.match(r'^(\d{3,7}) (.+)$', line)
        if not m:
            print("無法解析：", repr(line))
            continue
        code, name = m.group(1), m.group(2).strip()
        if len(code) == 3:
            head_offices[code] = name
        elif len(code) == 7:
            head_code = code[:3]
            branches[code] = {"name": name, "head_office": head_code}

print(f"總行數量: {len(head_offices)}")
print(f"分行數量: {len(branches)}")

with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
    f.write('# 總行資料：三碼銀行代碼\n')
    f.write('HEAD_OFFICES = {\n')
    for code, name in head_offices.items():
        f.write(f'    "{code}": "{name}",\n')
    f.write('}\n\n')
    f.write('# 分行資料：七碼銀行代碼\n')
    f.write('BRANCHES = {\n')
    for code, info in branches.items():
        f.write(f'    "{code}": {{"name": "{info["name"]}", "head_office": "{info["head_office"]}"}},\n')
    f.write('}\n\n')
    f.write('def get_head_offices():\n    return HEAD_OFFICES\n\n')
    f.write('def get_branches(head_code):\n    return [\n        {"code": code, "name": info["name"]}\n        for code, info in BRANCHES.items()\n        if info["head_office"] == head_code\n    ]\n\n')
    f.write('def get_branch_info(branch_code):\n    info = BRANCHES.get(branch_code)\n    if not info:\n        return None\n    head_name = HEAD_OFFICES.get(info["head_office"], "未知總行")\n    return {"code": branch_code, "name": info["name"], "head_office": info["head_office"], "head_office_name": head_name}\n\n')