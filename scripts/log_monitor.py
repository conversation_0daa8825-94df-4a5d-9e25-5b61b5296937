#!/usr/bin/env python3
"""日誌監控工具"""

import os
from datetime import datetime

def monitor_logs():
    """監控日誌文件"""
    print("📊 日誌系統監控")
    print("=" * 50)
    
    log_files = [
        ('logs/accounting.log', '應用日誌'),
        ('logs/access.log', '訪問日誌'),
        ('logs/error.log', '錯誤日誌')
    ]
    
    for log_file, description in log_files:
        if os.path.exists(log_file):
            size = os.path.getsize(log_file)
            mtime = os.path.getmtime(log_file)
            last_modified = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
            
            print(f"📄 {description}")
            print(f"   文件: {log_file}")
            print(f"   大小: {size:,} bytes")
            print(f"   最後修改: {last_modified}")
            
            # 顯示最後幾行
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print("   最新記錄:")
                        for line in lines[-2:]:
                            print(f"     {line.strip()}")
                    else:
                        print("   📝 文件為空")
            except Exception as e:
                print(f"   ❌ 讀取失敗: {e}")
            
            print()
        else:
            print(f"❌ {description} ({log_file}) - 文件不存在")
    
    print("=" * 50)
    print("🔧 日誌管理命令:")
    print("  查看實時日誌: tail -f logs/accounting.log")
    print("  查看訪問日誌: tail -f logs/access.log")
    print("  查看錯誤日誌: tail -f logs/error.log")
    print("  清理舊日誌: find logs/ -name '*.log.*' -mtime +30 -delete")

if __name__ == "__main__":
    monitor_logs()