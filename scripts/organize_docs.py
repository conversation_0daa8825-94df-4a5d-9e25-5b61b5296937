#!/usr/bin/env python3
"""整理文檔文件到 docs 文件夾"""

import shutil
from pathlib import Path

def organize_docs():
    """整理所有 .md 文件到 docs 文件夾"""
    
    # 創建 docs 文件夾
    docs_dir = Path("docs")
    docs_dir.mkdir(exist_ok=True)
    
    # 找出所有 .md 文件（排除 .git 文件夾）
    md_files = []
    for file_path in Path(".").rglob("*.md"):
        if ".git" not in str(file_path):
            md_files.append(file_path)
    
    print(f"🔍 找到 {len(md_files)} 個 .md 文件")
    
    # 按類別分組
    categories = {
        "migration": [],
        "security": [],
        "performance": [],
        "testing": [],
        "general": []
    }
    
    for file_path in md_files:
        filename = file_path.name.lower()
        
        if any(keyword in filename for keyword in ["migration", "migrate"]):
            categories["migration"].append(file_path)
        elif any(keyword in filename for keyword in ["security", "sql", "injection"]):
            categories["security"].append(file_path)
        elif any(keyword in filename for keyword in ["performance", "perf", "load"]):
            categories["performance"].append(file_path)
        elif any(keyword in filename for keyword in ["test", "route"]):
            categories["testing"].append(file_path)
        else:
            categories["general"].append(file_path)
    
    # 移動文件到對應的子文件夾
    moved_files = []
    
    for category, files in categories.items():
        if files:
            category_dir = docs_dir / category
            category_dir.mkdir(exist_ok=True)
            
            print(f"\n📁 {category.upper()} 類別:")
            for file_path in files:
                try:
                    # 檢查目標文件是否已存在
                    target_path = category_dir / file_path.name
                    if target_path.exists():
                        print(f"⚠️  {file_path.name} - 目標已存在，跳過")
                        continue
                    
                    # 移動文件
                    shutil.move(str(file_path), str(target_path))
                    moved_files.append((str(file_path), str(target_path)))
                    print(f"✅ {file_path.name} -> docs/{category}/")
                    
                except Exception as e:
                    print(f"❌ 移動 {file_path.name} 失敗: {e}")
    
    # 創建總索引文件
    create_index_file(docs_dir, categories, moved_files)
    
    print(f"\n🎉 完成！共移動了 {len(moved_files)} 個文件到 docs/ 文件夾")
    return moved_files

def create_index_file(docs_dir, categories, moved_files):
    """創建文檔索引文件"""
    
    index_content = """# 📚 會計系統文檔索引

本文件夾包含會計系統的所有技術文檔。

## 📁 文檔分類

### 🔄 Migration (遷移相關)
系統架構遷移、代碼重構相關文檔。

### 🔒 Security (安全性)
安全性測試、SQL 注入防護、安全分析報告。

### ⚡ Performance (性能)
性能測試、負載測試、優化建議相關文檔。

### 🧪 Testing (測試)
測試策略、測試結果、路由測試相關文檔。

### 📋 General (一般文檔)
README、URL 映射、一般性說明文檔。

## 📖 文檔列表

"""
    
    for category, files in categories.items():
        if files:
            index_content += f"\n### {category.upper()}\n"
            category_files = [f for f in moved_files if f[1].startswith(f"docs/{category}/")]
            for original, target in category_files:
                filename = Path(target).name
                index_content += f"- [{filename}](./{category}/{filename})\n"
    
    index_content += """
## 🔧 使用說明

1. **查看特定類別文檔**: 進入對應的子文件夾
2. **搜索文檔**: 使用 `grep -r "關鍵字" docs/`
3. **更新文檔**: 直接編輯對應的 .md 文件

## 📝 文檔維護

- 新增文檔時請放入適當的分類文件夾
- 重要變更請更新此索引文件
- 定期檢查文檔的時效性

---
*此索引文件由 organize_docs.py 自動生成*
"""
    
    index_path = docs_dir / "README.md"
    with open(index_path, 'w', encoding='utf-8') as f:
        f.write(index_content)
    
    print(f"📝 創建文檔索引: {index_path}")

if __name__ == "__main__":
    organize_docs()