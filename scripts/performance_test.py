#!/usr/bin/env python3
"""性能監控測試腳本"""

import time
import requests
from concurrent.futures import ThreadPoolExecutor

def test_performance_monitoring():
    """測試性能監控功能"""
    print("🧪 性能監控系統測試")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # 1. 測試性能監控API
    print("📊 測試性能監控API...")
    try:
        response = requests.get(f"{base_url}/admin/performance/api", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 性能API正常 - 總請求數: {data['performance_summary']['total_requests']}")
        else:
            print(f"❌ 性能API失敗: {response.status_code}")
    except Exception as e:
        print(f"❌ 無法連接到應用: {e}")
        print("💡 請先啟動應用: python app.py")
        return
    
    # 2. 生成負載測試
    print("\n🔄 生成負載測試...")
    test_routes = [
        "/",
        "/income_record",
        "/expense_record",
        "/company_setting",
        "/account_setting"
    ]
    
    def make_request(route):
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}{route}", timeout=10)
            duration = (time.time() - start_time) * 1000
            return {
                'route': route,
                'status': response.status_code,
                'duration': duration
            }
        except Exception as e:
            return {
                'route': route,
                'status': 'ERROR',
                'duration': 0,
                'error': str(e)
            }
    
    # 並發測試
    print("🚀 執行並發測試 (10個並發用戶)...")
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = []
        for _ in range(20):  # 20個請求
            for route in test_routes:
                futures.append(executor.submit(make_request, route))
        
        results = [future.result() for future in futures]
    
    # 分析結果
    successful_requests = [r for r in results if r['status'] == 200]
    failed_requests = [r for r in results if r['status'] != 200]
    
    if successful_requests:
        avg_duration = sum(r['duration'] for r in successful_requests) / len(successful_requests)
        max_duration = max(r['duration'] for r in successful_requests)
        min_duration = min(r['duration'] for r in successful_requests)
        
        print(f"✅ 成功請求: {len(successful_requests)}")
        print(f"📊 平均響應時間: {avg_duration:.2f}ms")
        print(f"📊 最大響應時間: {max_duration:.2f}ms")
        print(f"📊 最小響應時間: {min_duration:.2f}ms")
    
    if failed_requests:
        print(f"❌ 失敗請求: {len(failed_requests)}")
    
    # 3. 檢查性能監控數據
    print("\n📈 檢查性能監控數據...")
    time.sleep(2)  # 等待數據更新
    
    try:
        response = requests.get(f"{base_url}/admin/performance/api")
        if response.status_code == 200:
            data = response.json()
            summary = data['performance_summary']
            
            print(f"📊 系統運行時間: {summary['uptime_seconds']:.2f}秒")
            print(f"📊 總請求數: {summary['total_requests']}")
            print(f"📊 平均響應時間: {summary['avg_response_time']:.2f}ms")
            print(f"📊 慢請求數量: {summary['slow_requests_count']}")
            
            if summary['top_endpoints']:
                print("\n🔥 熱門端點:")
                for endpoint in summary['top_endpoints'][:3]:
                    print(f"  {endpoint['endpoint']}: {endpoint['count']} 次請求")
            
            if data['slow_requests']:
                print(f"\n🐌 慢請求記錄: {len(data['slow_requests'])} 個")
    
    except Exception as e:
        print(f"❌ 獲取性能數據失敗: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 性能監控測試完成！")
    print("\n📋 使用說明:")
    print("1. 訪問性能儀表板: http://localhost:5000/admin/performance")
    print("2. 查看性能API: http://localhost:5000/admin/performance/api")
    print("3. 導出性能數據: http://localhost:5000/admin/performance/export")

if __name__ == "__main__":
    test_performance_monitoring()