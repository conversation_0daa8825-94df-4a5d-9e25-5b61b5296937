#!/usr/bin/env python3
"""測試運行腳本"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """運行命令並顯示結果"""
    print(f"\n{'='*60}")
    print(f"🔍 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("錯誤輸出:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"執行失敗: {e}")
        return False

def main():
    """主要測試運行函數"""
    print("🧪 會計系統測試套件")
    print("=" * 60)
    
    # 檢查是否在正確的目錄
    if not os.path.exists('tests'):
        print("❌ 找不到 tests 目錄，請在項目根目錄運行此腳本")
        sys.exit(1)
    
    # 測試類別和描述
    test_categories = [
        ("tests/test_models.py", "📊 資料模型測試", "測試資料庫模型和關係"),
        ("tests/test_business_logic.py", "🧮 業務邏輯測試", "測試會計規則和業務邏輯"),
        ("tests/test_quick_fixes.py", "⚡ 快速功能測試", "測試目前可正常工作的功能"),
        ("tests/test_routes.py", "🛣️  路由測試", "測試原有的路由功能"),
    ]
    
    results = []
    
    for test_file, title, description in test_categories:
        print(f"\n{title}")
        print(f"📝 {description}")
        print("-" * 40)
        
        if os.path.exists(test_file):
            success = run_command(f"python -m pytest {test_file} -v", f"運行 {test_file}")
            results.append((title, success))
        else:
            print(f"⚠️  文件不存在: {test_file}")
            results.append((title, False))
    
    # 顯示總結
    print(f"\n{'='*60}")
    print("📋 測試結果總結")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for title, success in results:
        status = "✅ 通過" if success else "❌ 失敗"
        print(f"{status} {title}")
        if success:
            passed += 1
    
    print(f"\n📊 總體結果: {passed}/{total} 個測試類別通過")
    
    if passed == total:
        print("🎉 所有測試都通過了！")
    else:
        print("⚠️  有一些測試失敗，請查看上面的詳細信息")
    
    # 額外的有用命令
    print(f"\n{'='*60}")
    print("🔧 其他有用的測試命令")
    print(f"{'='*60}")
    print("運行所有測試:")
    print("  python -m pytest tests/ -v")
    print("\n運行特定測試:")
    print("  python -m pytest tests/test_models.py::TestUser::test_create_user -v")
    print("\n查看測試覆蓋率:")
    print("  pip install pytest-cov")
    print("  python -m pytest tests/ --cov=. --cov-report=html")
    print("\n只運行失敗的測試:")
    print("  python -m pytest tests/ --lf")
    print("\n運行測試並在第一個失敗時停止:")
    print("  python -m pytest tests/ -x")

if __name__ == "__main__":
    main()