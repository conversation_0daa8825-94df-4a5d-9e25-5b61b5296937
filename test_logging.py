#!/usr/bin/env python3
"""測試日誌系統"""

import time
import logging
from app import create_app

def test_logging_system():
    """測試日誌系統功能"""
    print("🧪 測試日誌系統...")
    
    # 創建應用
    app = create_app()
    
    with app.app_context():
        # 測試應用日誌
        app.logger.info("這是一條測試信息")
        app.logger.warning("這是一條測試警告")
        app.logger.error("這是一條測試錯誤")
        
        # 測試安全日誌
        security_logger = logging.getLogger('security')
        security_logger.warning("檢測到可疑活動: SQL注入嘗試")
        
        # 測試性能日誌
        performance_logger = logging.getLogger('performance')
        performance_logger.info("慢查詢檢測: SELECT * FROM users - 耗時: 1500ms")
        
        # 測試訪問日誌
        access_logger = logging.getLogger('access')
        access_logger.info("127.0.0.1 - GET /dashboard - 200 - 250 ms")
        
        print("✅ 日誌測試完成")

def test_web_requests():
    """測試Web請求日誌"""
    print("🌐 測試Web請求日誌...")
    
    app = create_app()
    client = app.test_client()
    
    # 測試幾個請求
    test_routes = [
        '/',
        '/income_record',
        '/expense_record',
        '/accounting/subject_manage'
    ]
    
    for route in test_routes:
        response = client.get(route)
        print(f"✅ 測試路由: {route} - 狀態: {response.status_code}")
        time.sleep(0.1)  # 短暫延遲
    
    print("✅ Web請求測試完成")

def check_log_files():
    """檢查日誌文件"""
    print("📁 檢查日誌文件...")
    
    import os
    
    expected_logs = [
        'logs/accounting.log',
        'logs/error.log', 
        'logs/access.log',
        'logs/security.log',
        'logs/performance.log'
    ]
    
    for log_file in expected_logs:
        if os.path.exists(log_file):
            size = os.path.getsize(log_file)
            print(f"✅ {log_file} - 大小: {size} bytes")
        else:
            print(f"❌ {log_file} - 不存在")
    
    print("✅ 日誌文件檢查完成")

def show_log_samples():
    """顯示日誌樣本"""
    print("📋 顯示日誌樣本...")
    
    import os
    
    log_files = [
        ('logs/accounting.log', '應用日誌'),
        ('logs/access.log', '訪問日誌'),
        ('logs/security.log', '安全日誌'),
        ('logs/performance.log', '性能日誌')
    ]
    
    for log_file, description in log_files:
        if os.path.exists(log_file):
            print(f"\n📄 {description} ({log_file}):")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # 顯示最後3行
                    for line in lines[-3:]:
                        print(f"  {line.strip()}")
            except Exception as e:
                print(f"  ❌ 讀取失敗: {e}")
        else:
            print(f"\n📄 {description}: 文件不存在")

if __name__ == "__main__":
    print("🔍 日誌系統測試開始...")
    print("=" * 50)
    
    # 測試日誌系統
    test_logging_system()
    print()
    
    # 測試Web請求
    test_web_requests()
    print()
    
    # 檢查日誌文件
    check_log_files()
    print()
    
    # 顯示日誌樣本
    show_log_samples()
    
    print("\n" + "=" * 50)
    print("🎉 日誌系統測試完成！")
    print("\n📋 使用說明:")
    print("1. 查看應用日誌: tail -f logs/accounting.log")
    print("2. 查看訪問日誌: tail -f logs/access.log") 
    print("3. 查看安全日誌: tail -f logs/security.log")
    print("4. 查看性能日誌: tail -f logs/performance.log")
    print("5. 查看錯誤日誌: tail -f logs/error.log")