from database import get_db
from model import AccountSubject
from sqlalchemy.orm import joinedload
from collections import OrderedDict
from typing import List, Dict, Optional

class SubjectService:
    """科目管理服務類"""
    
    @staticmethod
    def get_all_subjects_by_category() -> Dict[str, List[AccountSubject]]:
        """取得所有科目按分類分組"""
        category_order = [
            '資產', '負債', '權益', '營業收入', '營業成本',
            '營業費用', '營業外收益及費損', '所得稅'
        ]
        
        with get_db() as db:
            # 查詢所有科目，預先載入 children
            all_subjects = db.query(AccountSubject).options(
                joinedload(AccountSubject.children)
            ).all()
            
            # 依 top_category 分組
            category_dict = OrderedDict()
            for cat in category_order:
                category_dict[cat] = []
                
            for subj in all_subjects:
                cat = subj.top_category or '其他'
                if cat in category_dict and subj.parent_id is None:
                    category_dict[cat].append(subj)
        
        return category_dict
    
    @staticmethod
    def create_subject(name: str, code: str, note: str = None, 
                      parent_code: str = None) -> AccountSubject:
        """建立新科目"""
        with get_db() as db:
            parent = None
            full_code = code
            
            if parent_code:
                parent = db.query(AccountSubject).filter_by(code=parent_code).first()
                if parent:
                    full_code = f"{parent.code}{code}"
                    top_category = parent.top_category
                else:
                    top_category = None
            else:
                top_category = None
                
            new_subject = AccountSubject(
                name=name,
                code=full_code,
                note=note,
                parent_id=parent.id if parent else None,
                top_category=top_category
            )
            db.add(new_subject)
            db.flush()  # 取得 ID
            return new_subject
    
    @staticmethod
    def get_subject_by_code(code: str) -> Optional[AccountSubject]:
        """根據代碼取得科目"""
        with get_db() as db:
            return db.query(AccountSubject).filter_by(code=code).first()
    
    @staticmethod
    def update_subject(code: str, name: str, note: str = None) -> bool:
        """更新科目"""
        with get_db() as db:
            subject = db.query(AccountSubject).filter_by(code=code).first()
            if not subject:
                return False
                
            subject.name = name
            if note is not None:
                subject.note = note
            return True
    
    @staticmethod
    def delete_subject(code: str) -> bool:
        """刪除科目"""
        with get_db() as db:
            subject = db.query(AccountSubject).filter_by(code=code).first()
            if not subject:
                return False
                
            db.delete(subject)
            return True
    
    @staticmethod
    def get_next_sub_code(parent_code: str) -> str:
        """取得下一個可用的子科目代碼"""
        with get_db() as db:
            parent = db.query(AccountSubject).filter_by(code=parent_code).first()
            if not parent:
                return '001'
                
            used_codes = set()
            for child in parent.children:
                try:
                    code_int = int(child.code[-3:])
                    used_codes.add(code_int)
                except ValueError:
                    pass
                    
            for i in range(1, 1000):
                code_str = f"{i:03d}"
                if int(code_str) not in used_codes:
                    return code_str
                    
            return '001'  # 預設值 